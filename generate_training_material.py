#!/usr/bin/env python3
"""
Professional AI Training Material Generator
Creates comprehensive training materials for AI beginners and tech laypersons
"""

import os
import sys
import json
from datetime import datetime
from jinja2 import Template
from weasyprint import HTML, CSS
from weasyprint.text.fonts import FontConfiguration
import markdown

class AITrainingMaterialGenerator:
    def __init__(self):
        self.font_config = FontConfiguration()
        
    def load_existing_content(self):
        """Load and analyze existing training materials"""
        content = {
            'cybersecurity': self._extract_html_content('ai-cybersecurity-training-2025.html'),
            'office_usage': self._extract_html_content('ai-office-training-2025.html'),
            'meeting_notes': self._extract_meeting_notes()
        }
        return content
    
    def _extract_html_content(self, filename):
        """Extract key content from HTML files"""
        if not os.path.exists(filename):
            return {}
        
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract title and key sections (simplified extraction)
        title_start = content.find('<title>') + 7
        title_end = content.find('</title>')
        title = content[title_start:title_end] if title_start > 6 else "AI Training"
        
        return {
            'title': title,
            'content': content
        }
    
    def _extract_meeting_notes(self):
        """Extract key insights from meeting transcripts"""
        notes = {}
        
        # Extract from your provided notes
        notes['key_insights'] = [
            "Online školení je náročné - nejde navázat kontakt, směřuje k monotonosti",
            "Kybernetická bezpečnost - phishing nárůst o 442%",
            "AI okopíruje už cokoliv - pozor na kopie webovek",
            "Neexistuje možnost, jak poznat AI text",
            "Rychločtení je nejlepší skill v dnešní době",
            "AI ovládá browser - tohle je budoucnost"
        ]
        
        notes['security_tips'] = [
            "Neklikat na linky/tlačítka v emailech",
            "Kontrolovat odesílatele - banky neposílají maily",
            "Otevřením se nic neděje, ale pozor na přílohy",
            "Nastavit 2-faktor ověření",
            "Dělat zálohy všude všeho",
            "Používat 16 znakové heslo"
        ]
        
        notes['ai_tools'] = [
            "Eleven Labs - asistentka",
            "NotebookLM prezentace",
            "Cerebras.ai - rychlost generování",
            "AlphaFold - využití AI ve vědě"
        ]
        
        return notes
    
    def generate_comprehensive_material(self):
        """Generate comprehensive training material for 3-hour training"""

        # Load existing content
        existing_content = self.load_existing_content()

        # Define the comprehensive training structure - 3 hours total
        training_content = {
            'title': 'Umělá Inteligence pro Začátečníky - Kompletní Školící Materiál 2025',
            'subtitle': 'Praktický průvodce pro uživatele bez předchozí zkušenosti s AI',
            'date': '26.06.2025',
            'target_audience': [
                'Uživatelé bez předchozí zkušenosti s AI',
                'Technologicky méně zdatní uživatelé',
                'AI začátečníci a technologičtí laici'
            ],
            'modules': [
                {
                    'title': '1. Úvod do Umělé Inteligence - Demystifikace AI',
                    'duration': '45 minut',
                    'content': self._generate_intro_module()
                },
                {
                    'title': '2. Základy Práce s AI Nástroji - První Kroky',
                    'duration': '30 minut',
                    'content': self._generate_basics_module()
                },
                {
                    'title': '3. Kybernetická Bezpečnost v Éře AI',
                    'duration': '30 minut',
                    'content': self._generate_security_module(existing_content['meeting_notes'])
                },
                {
                    'title': '4. Praktické Využití AI v Kanceláři a Každodenním Životě',
                    'duration': '45 minut',
                    'content': self._generate_office_module()
                },
                {
                    'title': '5. Nejlepší AI Nástroje a Doporučení pro Začátečníky',
                    'duration': '30 minut',
                    'content': self._generate_tools_module(existing_content['meeting_notes'])
                },
                {
                    'title': '6. Budoucnost AI a Vaše Další Kroky',
                    'duration': '20 minut',
                    'content': self._generate_future_module()
                }
            ]
        }

        return training_content
    
    def _generate_intro_module(self):
        return {
            'objectives': [
                'Pochopit, co je umělá inteligence a jak funguje',
                'Rozpoznat AI v každodenním životě a práci',
                'Překonat strach z nových technologií',
                'Naučit se základní terminologii AI',
                'Pochopit možnosti a omezení AI'
            ],
            'content': [
                {
                    'type': 'text',
                    'title': 'Co je Umělá Inteligence? - Jednoduché vysvětlení',
                    'text': '''
                    Umělá inteligence (AI) je technologie, která umožňuje počítačům "myslet" a řešit problémy podobně jako lidé.
                    Není to magie ani science fiction - je to pokročilý software, který se naučil rozpoznávat vzory a poskytovat užitečné odpovědi.

                    **Představte si AI jako velmi chytrého asistenta, který:**
                    - Nikdy neunává a pracuje 24/7
                    - Má přístup k obrovskému množství informací
                    - Dokáže rychle najít vzory a souvislosti
                    - Učí se z každé interakce
                    - Ale stále potřebuje vaše vedení a kontrolu
                    '''
                },
                {
                    'type': 'story',
                    'title': 'Příběh z praxe: Jak AI pomohla Marii',
                    'text': '''
                    Marie (55 let) pracuje jako účetní a bála se, že AI ji nahradí. Místo toho se naučila používat AI pro:
                    - Automatické kategorizování faktur
                    - Psaní emailů klientům
                    - Kontrolu chyb v tabulkách

                    Výsledek: Ušetří 2 hodiny denně a může se věnovat důležitějším úkolům. AI se stala jejím nejlepším pomocníkem!
                    '''
                },
                {
                    'type': 'text',
                    'title': 'AI kolem nás - Příklady, které už znáte',
                    'text': '''
                    **V telefonu:**
                    - Automatické opravy textu při psaní
                    - Rozpoznávání obličejů na fotkách
                    - Hlasový asistent (Siri, Google Assistant)
                    - Prediktivní text při psaní zpráv

                    **Na internetu:**
                    - Doporučení na YouTube, Netflix, Spotify
                    - Automatické překlady na webových stránkách
                    - Spam filtr v emailu
                    - Vyhledávání na Googlu

                    **V každodenním životě:**
                    - Navigace v mapách s dopravními informacemi
                    - Doporučení produktů v e-shopech
                    - Automatické nastavení fotoaparátu
                    - Rozpoznávání hudby (Shazam)
                    '''
                },
                {
                    'type': 'exercise',
                    'title': 'Praktické cvičení: Najděte AI ve svém telefonu',
                    'text': '''
                    **Úkol pro účastníky (5 minut):**
                    1. Otevřete svůj telefon
                    2. Najděte alespoň 3 funkce, které používají AI
                    3. Zkuste použít hlasového asistenta
                    4. Podělte se o zkušenost se skupinou

                    **Tip:** Hledejte funkce jako "automatické", "chytré", "inteligentní"
                    '''
                },
                {
                    'type': 'text',
                    'title': 'Základní terminologie AI - Slovníček pro začátečníky',
                    'text': '''
                    **Prompt:** Váš požadavek nebo otázka pro AI (jako když se ptáte asistenta)

                    **Chatbot:** AI program, se kterým můžete chatovat jako s člověkem

                    **Machine Learning:** Způsob, jak se AI učí z dat (jako když se dítě učí chodit)

                    **Algoritmus:** Sada pravidel, podle kterých AI funguje (jako recept na vaření)

                    **Deepfake:** Falešné video nebo audio vytvořené AI (pozor na podvody!)

                    **GPT:** Typ AI, který rozumí a generuje text (jako ChatGPT)
                    '''
                },
                {
                    'type': 'warning',
                    'title': 'Důležité: Co AI NEUMÍ',
                    'text': '''
                    AI není všemocná! Má svá omezení:
                    - Nemá vlastní vědomí ani emoce
                    - Může dělat chyby a "halucinovat" (vymýšlet si)
                    - Nerozumí kontextu jako člověk
                    - Potřebuje jasné instrukce
                    - Nemůže nahradit lidské úsudek a kreativitu
                    '''
                },
                {
                    'type': 'highlight',
                    'title': 'Klíčové poselství',
                    'text': '''
                    AI není náhrada za lidské myšlení, ale mocný nástroj, který nám pomáhá být efektivnější.
                    Je to jako mít velmi chytrého asistenta, který nikdy neunává, ale stále potřebuje vaše vedení.
                    '''
                }
            ]
        }
    
    def _generate_basics_module(self):
        return {
            'objectives': [
                'Naučit se základy komunikace s AI',
                'Pochopit, jak psát efektivní prompty',
                'Vyzkoušet první AI nástroj prakticky',
                'Rozpoznat dobré a špatné způsoby komunikace s AI',
                'Získat sebevědomí při práci s AI'
            ],
            'content': [
                {
                    'type': 'text',
                    'title': 'Jak komunikovat s AI - Umění promptování',
                    'text': '''
                    Komunikace s AI je jako mluvení s velmi chytrým, ale trochu "autistickým" asistentem.
                    Potřebuje jasné a konkrétní instrukce.

                    **Základní pravidla pro práci s AI:**

                    1. **Buďte konkrétní a detailní**
                       - ❌ Špatně: "Napiš email"
                       - ✅ Dobře: "Napiš formální email kolegovi Petrovi o přeložení schůzky z úterý na čtvrtek"

                    2. **Používejte příklady a kontext**
                       - "Napiš to ve stylu, jako když píšete důležitému klientovi"
                       - "Použij stejný tón jako v tomto příkladu: [vložte příklad]"

                    3. **Iterujte a vylepšujte**
                       - První odpověď není finální
                       - Řekněte: "Udělej to kratší", "Přidej více detailů", "Změň tón na přátelštější"

                    4. **Buďte trpěliví a experimentujte**
                       - AI potřebuje čas na "přemýšlení"
                       - Zkuste různé způsoby, jak položit stejnou otázku
                    '''
                },
                {
                    'type': 'example',
                    'title': 'Příklad: Jak napsat dobrý prompt',
                    'text': '''
                    **Špatný prompt:**
                    "Napiš něco o počasí"

                    **Dobrý prompt:**
                    "Napiš krátký, přátelský příspěvek na Facebook o tom, jak krásné je dnes slunečné počasí.
                    Použij pozitivní tón, přidej emoji a povzbuď lidi, aby šli ven. Maximálně 50 slov."

                    **Výsledek:** Místo obecného textu dostanete přesně to, co potřebujete!
                    '''
                },
                {
                    'type': 'exercise',
                    'title': 'Praktické cvičení: Váš první prompt',
                    'text': '''
                    **Úkol (10 minut):**
                    1. Otevřete si ChatGPT nebo jiný AI nástroj
                    2. Zkuste tento prompt: "Vysvětli mi, jak funguje email, jako bych byl 5letý"
                    3. Pak zkuste: "Přepiš to pro dospělého začátečníka"
                    4. Porovnejte rozdíly v odpovědích

                    **Pozorujte:** Jak se mění styl a složitost vysvětlení
                    '''
                },
                {
                    'type': 'text',
                    'title': 'Časté chyby začátečníků a jak se jim vyhnout',
                    'text': '''
                    **❌ Chyba 1: Příliš obecné požadavky**
                    - "Pomoz mi s prací" → "Pomoz mi napsat prezentaci o výsledcích prodeje za Q4"

                    **❌ Chyba 2: Očekávání dokonalosti na první pokus**
                    - AI potřebuje zpětnou vazbu a upřesnění

                    **❌ Chyba 3: Strach z experimentování**
                    - AI se nezlobí, když se ptáte znovu nebo jinak

                    **❌ Chyba 4: Zapomínání na kontext**
                    - Řekněte AI, kdo jste a co potřebujete: "Jsem majitel malé firmy a potřebujem..."

                    **❌ Chyba 5: Neověřování informací**
                    - AI může dělat chyby, vždy si důležité informace ověřte
                    '''
                },
                {
                    'type': 'recommendation',
                    'title': 'Tipy pro úspěšnou komunikaci s AI',
                    'text': '''
                    **🎯 Zlaté pravidlo:** Mluvte s AI jako s velmi chytrým praktikantem

                    **📝 Struktura dobrého promptu:**
                    1. Kontext: "Jsem učitel základní školy..."
                    2. Úkol: "Potřebuji vytvořit..."
                    3. Specifikace: "Ve formátu..., délka..., styl..."
                    4. Příklad: "Podobně jako..."

                    **🔄 Iterativní přístup:**
                    - Začněte jednoduše
                    - Postupně přidávejte detaily
                    - Nechte AI navrhovat vylepšení
                    '''
                }
            ]
        }

    def _generate_security_module(self, meeting_notes):
        return {
            'objectives': [
                'Rozpoznat nová bezpečnostní rizika spojená s AI',
                'Naučit se praktické bezpečné návyky',
                'Chránit se před AI-generovanými podvody',
                'Pochopit, jak AI mění kybernetickou bezpečnost',
                'Získat nástroje pro ověřování informací'
            ],
            'content': [
                {
                    'type': 'warning',
                    'title': 'ALARMUJÍCÍ STATISTIKY - Proč je bezpečnost důležitější než kdy jindy',
                    'text': '''
                    **Phishing útoky vzrostly o 442% díky AI!**

                    AI umožňuje vytváření velmi přesvědčivých podvodů:
                    - Falešné emaily nerozpoznatelné od skutečných
                    - Deepfake videa celebrit a politiků
                    - Klonování hlasů za pár sekund
                    - Automatické generování tisíců podvodných webů denně
                    '''
                },
                {
                    'type': 'story',
                    'title': 'Skutečný příběh: Jak AI podvedla zkušeného IT specialistu',
                    'text': '''
                    Jan (45 let), IT manažer s 20letou praxí, dostal email od "svého šéfa" s žádostí o urgentní převod peněz.
                    Email vypadal dokonale - správný podpis, logo firmy, dokonce i osobní poznámka o Janově synovi.

                    **Co se stalo:** AI analyzovala veřejné informace o firmě a vytvořila dokonalý podvodný email.
                    **Škoda:** 50 000 Kč
                    **Ponaučení:** I experti mohou být oklamáni AI-generovanými podvody.
                    '''
                },
                {
                    'type': 'text',
                    'title': 'Nové hrozby v éře AI - Co musíte vědět',
                    'text': '''
                    **1. AI-generované phishing emaily**
                    - Dokonalá gramatika a styl
                    - Personalizované informace z veřejných zdrojů
                    - Napodobování známých osob a firem

                    **2. Deepfake technologie**
                    - Falešná videa politiků a celebrit
                    - Klonování hlasů z krátkých nahrávek
                    - Falešné videohovory s "rodinnými příslušníky"

                    **3. AI-powered sociální inženýrství**
                    - Analýza sociálních sítí pro cílené útoky
                    - Automatické vytváření falešných profilů
                    - Manipulace s emocemi pomocí AI

                    **4. Automatizované útoky**
                    - Tisíce pokusů o průlom za minutu
                    - Inteligentní přizpůsobování útočných strategií
                    - Obcházení tradičních bezpečnostních opatření
                    '''
                },
                {
                    'type': 'text',
                    'title': 'Zlatá pravidla kybernetické bezpečnosti v éře AI',
                    'text': '''
                    **🛡️ 12 ŽIVOTNĚ DŮLEŽITÝCH PRAVIDEL:**

                    1. **NIKDY neklikejte na odkazy v podezřelých emailech**
                       - Raději si stránku najděte sami v prohlížeči

                    2. **Kontrolujte odesílatele emailů**
                       - Banky a úřady neposílají náhodné emaily
                       - Ověřte si emailovou adresu odesílatele

                    3. **Otevření emailu je bezpečné, ale pozor na přílohy a tlačítka**
                       - Nestahujte podezřelé přílohy
                       - Neklikejte na tlačítka v emailech

                    4. **Používejte 2-faktorové ověření (2FA) VŠUDE**
                       - Gmail, Facebook, bankovnictví, e-shopy
                       - Je to jako druhý zámek na dveřích

                    5. **Zálohujte VŠE - pravidelně a na více míst**
                       - Cloudy, externí disky, USB
                       - Pravidlo 3-2-1: 3 kopie, 2 různá média, 1 mimo domov

                    6. **Používejte silná hesla - minimálně 16 znaků**
                       - Kombinace písmen, čísel a symbolů
                       - Jiné heslo pro každý účet

                    7. **Buďte skeptičtí k příliš dobrým nabídkám**
                       - "Vyhráli jste milion" = podvod
                       - "Investice s 100% ziskem" = podvod

                    8. **Ověřujte informace z více nezávislých zdrojů**
                       - Zejména u finančních a zdravotních rad
                       - Ptejte se přátel, rodiny, odborníků

                    9. **Aktualizujte software pravidelně**
                       - Windows Update, aplikace, antivirus
                       - Automatické aktualizace = váš přítel

                    10. **Důvěřujte svému instinktu**
                        - Pokud něco vypadá podezřele, pravděpodobně je
                        - Raději se zeptejte než riskujte

                    11. **Používejte správce hesel**
                        - Bitwarden, 1Password, LastPass
                        - Generuje a ukládá silná hesla

                    12. **Buďte opatrní na veřejných Wi-Fi**
                        - Nedělejte bankovnictví na veřejných sítích
                        - Používejte VPN pro citlivé aktivity
                    '''
                },
                {
                    'type': 'exercise',
                    'title': 'Praktické cvičení: Rozpoznání podvodného emailu',
                    'text': '''
                    **Úkol (10 minut):**
                    Podívejte se na tyto znaky podvodného emailu:

                    1. **Urgentnost:** "Okamžitě jednejte nebo ztratíte účet!"
                    2. **Gramatické chyby:** (AI už je ale téměř eliminuje)
                    3. **Podezřelá emailová adresa:** <EMAIL> místo @csob.cz
                    4. **Obecné oslovení:** "Vážený kliente" místo vašeho jména
                    5. **Žádost o citlivé údaje:** hesla, PIN kódy, čísla karet

                    **Diskuze:** Sdílejte zkušenosti s podvodnými emaily
                    '''
                },
                {
                    'type': 'highlight',
                    'title': 'AI a deepfakes - Jak rozpoznat falešná videa a audio',
                    'text': '''
                    **Varovné signály deepfake videí:**
                    - Nepřirozené mrkání nebo jeho absence
                    - Rozmazané okraje kolem obličeje
                    - Nesynchronní pohyb rtů s řečí
                    - Nekonzistentní osvětlení obličeje

                    **Zlaté pravidlo:** Pokud vidíte šokující video celebrity nebo politika,
                    ověřte si ho z oficiálních zdrojů před sdílením!
                    '''
                },
                {
                    'type': 'action',
                    'title': 'Akční plán: Co udělat HNED po školení',
                    'text': '''
                    **Úkoly na příštích 7 dní:**

                    1. **Dnes:** Zapněte 2FA na Gmailu a Facebooku
                    2. **Zítra:** Změňte hesla na 3 nejdůležitějších účtech
                    3. **Do 3 dnů:** Nainstalujte správce hesel
                    4. **Do týdne:** Udělejte zálohu důležitých dat
                    5. **Průběžně:** Buďte skeptičtí k emailům a zprávám

                    **Pamatujte:** Bezpečnost není jednorázová akce, ale životní styl!
                    '''
                }
            ]
        }

    def _generate_office_module(self):
        return {
            'objectives': [
                'Využít AI pro každodenní kancelářské úkoly',
                'Zefektivnit práci s dokumenty a komunikací',
                'Automatizovat rutinní činnosti',
                'Naučit se konkrétní prompty pro běžné situace',
                'Ušetřit 2-3 hodiny denně pomocí AI'
            ],
            'content': [
                {
                    'type': 'text',
                    'title': 'AI jako váš osobní asistent - Revoluce v kanceláři',
                    'text': '''
                    Představte si, že máte asistenta, který:
                    - Nikdy neunává a pracuje 24/7
                    - Píše rychleji než profesionální copywriter
                    - Ovládá všechny jazyky světa
                    - Nikdy nezapomene a má nekonečnou trpělivost
                    - Stojí méně než káva denně

                    **To je AI v roce 2025!**
                    '''
                },
                {
                    'type': 'text',
                    'title': '📧 Psaní a komunikace - Staňte se mistrem emailů',
                    'text': '''
                    **Tvorba emailů a dopisů:**
                    - Formální emaily klientům a partnerům
                    - Neformální zprávy kolegům
                    - Omluvy, pozvánky, poděkování
                    - Reklamace a stížnosti
                    - Newslettery a hromadné zprávy

                    **Překlady textů:**
                    - Okamžité překlady do 100+ jazyků
                    - Zachování formálního/neformálního tónu
                    - Překlady technických dokumentů
                    - Lokalizace pro různé kultury

                    **Korektura a zlepšování stylu:**
                    - Oprava gramatických chyb
                    - Zlepšení stylu a čitelnosti
                    - Přizpůsobení tónu cílové skupině
                    - Zkracování nebo prodlužování textů

                    **Shrnutí dlouhých dokumentů:**
                    - Výtahy z reportů a analýz
                    - Klíčové body z meetingů
                    - Souhrny právních dokumentů
                    - Rychlé přehledy článků a studií
                    '''
                },
                {
                    'type': 'example',
                    'title': 'Konkrétní příklady promptů pro emaily',
                    'text': '''
                    **Situace 1: Zpoždění dodávky**
                    **Prompt:** "Napiš profesionální email klientovi, ve kterém se omluvíme za 3denní zpoždění dodávky kvůli technickým problémům. Nabídni 10% slevu jako kompenzaci a ujisti ho o kvalitě. Tón: empatický ale profesionální."

                    **Situace 2: Pozvánka na meeting**
                    **Prompt:** "Vytvoř pozvánku na týmový meeting v úterý 15.1. od 14:00 do 15:30. Téma: Plánování Q1 2025. Místnost: Konferenční sál A. Přidej agendu: 1) Přehled cílů, 2) Rozdělení úkolů, 3) Diskuze."

                    **Situace 3: Poděkování po meetingu**
                    **Prompt:** "Napiš děkovný email účastníkům včerejšího workshopu o AI. Poděkuj za aktivní účast, připomeň klíčové závěry a přilož prezentaci. Tón: vřelý a profesionální."
                    '''
                },
                {
                    'type': 'text',
                    'title': '📊 Analýza a zpracování dat - Staňte se data analytikem',
                    'text': '''
                    **Vytváření grafů a tabulek:**
                    - Automatické zpracování CSV souborů
                    - Vytváření přehledných grafů
                    - Porovnání dat mezi obdobími
                    - Identifikace trendů a anomálií

                    **Analýza trendů:**
                    - Rozpoznávání vzorů v datech
                    - Predikce budoucího vývoje
                    - Identifikace příležitostí a rizik
                    - Doporučení na základě dat

                    **Automatické reporty:**
                    - Měsíční a čtvrtletní přehledy
                    - KPI dashboardy
                    - Analýzy výkonnosti
                    - Competitive intelligence
                    '''
                },
                {
                    'type': 'text',
                    'title': '🎨 Kreativní práce - Uvolněte svou kreativitu',
                    'text': '''
                    **Návrhy prezentací:**
                    - Struktura a obsah slidů
                    - Návrhy designu a layoutu
                    - Storytelling a narativní linka
                    - Interaktivní prvky

                    **Tvorba obrázků a grafiky:**
                    - Loga a branding materiály
                    - Infografiky a diagramy
                    - Ilustrace a ikony
                    - Sociální média content

                    **Brainstorming nápadů:**
                    - Kreativní řešení problémů
                    - Návrhy kampaní a projektů
                    - Inovativní přístupy
                    - Out-of-the-box myšlení
                    '''
                },
                {
                    'type': 'story',
                    'title': 'Příběh z praxe: Jak AI změnila práci Petry',
                    'text': '''
                    **Petra (42 let), marketingová manažerka:**

                    **Před AI:**
                    - 3 hodiny denně psaní emailů a reportů
                    - Stres z gramatických chyb v angličtině
                    - Nedostatek času na kreativní práci

                    **Po zavedení AI:**
                    - 30 minut denně na psaní (AI píše, ona upravuje)
                    - Perfektní angličtina díky AI překladům
                    - 2,5 hodiny denně na strategickou práci

                    **Výsledek:** Zvýšení produktivity o 300%, povýšení na senior pozici!
                    '''
                },
                {
                    'type': 'exercise',
                    'title': 'Praktické cvičení: Napište email s AI',
                    'text': '''
                    **Úkol (15 minut):**

                    **Scénář:** Jste manažer a potřebujete napsat email týmu o změně termínu důležité prezentace.

                    **Vaše situace:**
                    - Prezentace pro vedení se přesouvá z pátku na úterý
                    - Důvod: Nemoc CEO
                    - Potřebujete ujistit tým, že je vše v pořádku
                    - Požádejte o potvrzení dostupnosti

                    **Zkuste napsat prompt a porovnejte výsledky s ostatními**
                    '''
                },
                {
                    'type': 'recommendation',
                    'title': 'Hotové prompty pro každodenní použití',
                    'text': '''
                    **📋 Zkopírujte si tyto prompty:**

                    **Pro shrnutí meetingu:**
                    "Vytvoř strukturované shrnutí z těchto poznámek z meetingu: [vložte poznámky]. Rozděl na: Klíčové závěry, Akční body s odpovědnými osobami, Termíny, Další kroky."

                    **Pro zlepšení textu:**
                    "Přepiš tento text tak, aby byl profesionálnější, jasnější a stručnější: [vložte text]"

                    **Pro brainstorming:**
                    "Potřebuji 10 kreativních nápadů na [téma]. Cílová skupina: [popište]. Rozpočet: [částka]. Buď inovativní ale realistický."

                    **Pro analýzu dat:**
                    "Analyzuj tato data a najdi 3 nejdůležitější trendy: [vložte data]. Vysvětli, co to znamená pro naše podnikání."
                    '''
                }
            ]
        }

    def _generate_tools_module(self, meeting_notes):
        return {
            'objectives': [
                'Poznat nejlepší AI nástroje',
                'Vybrat vhodný nástroj pro úkol',
                'Začít používat AI prakticky'
            ],
            'content': [
                {
                    'type': 'text',
                    'title': 'Doporučené AI nástroje pro začátečníky',
                    'text': '''
                    **Bezplatné nástroje:**

                    1. **ChatGPT (OpenAI)** - Univerzální asistent pro text
                       - Zdarma s omezeními
                       - Nejznámější a nejpoužívanější

                    2. **Google Gemini** - Integrovaný s Google službami
                       - Zdarma měsíc na zkoušku
                       - Výborný pro vyhledávání a analýzu

                    3. **Claude (Anthropic)** - Nejbezpečnější volba
                       - Netrénuje na vašich datech
                       - Výborný pro dlouhé texty

                    4. **NotebookLM** - Vytváření prezentací a podcastů
                       - Zdarma od Googlu
                       - Umí česky
                    '''
                },
                {
                    'type': 'recommendation',
                    'title': 'Doporučení pro začátek',
                    'text': '''
                    **Začněte s jedním nástrojem** a naučte se ho dobře používat.

                    **Neplaťte nic na rok** - v době AI se vše rychle mění.

                    **Vyzkoušejte zdarma verze** před nákupem placených verzí.
                    '''
                }
            ]
        }

    def _generate_future_module(self):
        return {
            'objectives': [
                'Pochopit trendy v AI',
                'Připravit se na budoucnost',
                'Pokračovat v učení'
            ],
            'content': [
                {
                    'type': 'text',
                    'title': 'Budoucnost AI',
                    'text': '''
                    **Co nás čeká:**

                    - **AI agenti** - automatizace celých pracovních procesů
                    - **Hlasové asistenty** - nerozpoznatelní od lidí
                    - **Personalizované AI** - přizpůsobené vašim potřebám
                    - **AI v prohlížečích** - automatické vyplňování formulářů

                    **Nejdůležitější dovednost:** Rychločtení a ověřování informací
                    '''
                },
                {
                    'type': 'action',
                    'title': 'Vaše další kroky',
                    'text': '''
                    1. **Vyberte si jeden AI nástroj** a používejte ho týden
                    2. **Experimentujte** s různými typy úkolů
                    3. **Sdílejte zkušenosti** s kolegy
                    4. **Sledujte novinky** - AI se vyvíjí velmi rychle
                    5. **Buďte trpěliví** - učení nových technologií chce čas
                    '''
                }
            ]
        }

    def generate_html_template(self):
        """Generate modern, flashy HTML template for sales-oriented presentation"""
        return '''
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ content.title }}</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.7;
            color: #1a1a1a;
            background: #f8fafc;
            font-size: 16px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            border-radius: 20px;
            overflow: hidden;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        /* Modern Header with Gradient and Effects */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            color: white;
            padding: 60px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 20%, rgba(255,255,255,0.2) 0%, transparent 50%),
                        radial-gradient(circle at 70% 80%, rgba(255,255,255,0.1) 0%, transparent 50%);
            opacity: 0.8;
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .header h1 {
            font-size: 3.5em;
            margin-bottom: 20px;
            font-weight: 800;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #fff, #e0e7ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .header h2 {
            font-size: 1.4em;
            opacity: 0.95;
            font-weight: 400;
            margin-bottom: 30px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        /* Modern Meta Info */
        .meta-info {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 40px;
            border-bottom: 1px solid #e2e8f0;
            position: relative;
        }

        .meta-info::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 6px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 0 3px 3px 0;
        }

        .meta-info h3 {
            color: #1e293b;
            margin-bottom: 20px;
            font-size: 1.3em;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .meta-info h3::before {
            content: '🎯';
            font-size: 1.2em;
        }

        .target-audience {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 25px;
        }

        .audience-tag {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 0.95em;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            transition: transform 0.2s ease;
        }

        .meta-date {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            display: inline-flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
            color: #1e293b;
        }

        .meta-date::before {
            content: '📅';
            font-size: 1.2em;
        }

        /* Content Area */
        .content {
            padding: 60px 40px;
        }

        /* Modern Module Design */
        .module {
            margin-bottom: 60px;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            page-break-inside: avoid;
        }

        .module-header {
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            color: white;
            padding: 30px;
            position: relative;
            overflow: hidden;
        }

        .module-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .module-title {
            font-size: 1.8em;
            margin-bottom: 10px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 15px;
            position: relative;
            z-index: 2;
        }

        .module-number {
            background: rgba(255,255,255,0.2);
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 800;
            font-size: 1.2em;
            backdrop-filter: blur(10px);
        }

        .module-duration {
            opacity: 0.9;
            font-size: 1em;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative;
            z-index: 2;
        }

        .module-duration::before {
            content: '⏱️';
            font-size: 1.1em;
        }

        .module-content {
            padding: 40px;
            background: white;
        }

        /* Objectives with Modern Design */
        .objectives {
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 35px;
            border: 1px solid #a7f3d0;
            position: relative;
        }

        .objectives::before {
            content: '🎯';
            position: absolute;
            top: -10px;
            left: 20px;
            background: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 1.2em;
        }

        .objectives h4 {
            color: #065f46;
            margin-bottom: 20px;
            font-size: 1.2em;
            font-weight: 600;
            margin-left: 20px;
        }

        .objectives ul {
            list-style: none;
            padding-left: 0;
        }

        .objectives li {
            padding: 8px 0;
            position: relative;
            padding-left: 35px;
            font-weight: 500;
            color: #047857;
        }

        .objectives li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #10b981;
            font-weight: bold;
            font-size: 1.2em;
            background: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }

        /* Content Items */
        .content-item {
            margin-bottom: 35px;
        }

        .content-title {
            font-size: 1.4em;
            color: #1e293b;
            margin-bottom: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 12px;
            position: relative;
        }

        .content-title::before {
            content: '';
            width: 4px;
            height: 30px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .content-text {
            line-height: 1.8;
            font-size: 1.05em;
            color: #374151;
        }

        .content-text strong {
            color: #1e293b;
            font-weight: 600;
        }

        /* Modern Alert Boxes */
        .highlight {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
            box-shadow: 0 8px 25px rgba(251, 191, 36, 0.3);
            position: relative;
            overflow: hidden;
        }

        .highlight::before {
            content: '💡';
            position: absolute;
            top: -8px;
            right: 20px;
            background: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 1.2em;
        }

        .warning {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
            position: relative;
            overflow: hidden;
        }

        .warning::before {
            content: '⚠️';
            position: absolute;
            top: -8px;
            right: 20px;
            background: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 1.2em;
        }

        .exercise {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
            box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
            position: relative;
            overflow: hidden;
        }

        .exercise::before {
            content: '🎯';
            position: absolute;
            top: -8px;
            right: 20px;
            background: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 1.2em;
        }

        .example {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px solid #06b6d4;
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            position: relative;
            box-shadow: 0 4px 15px rgba(6, 182, 212, 0.1);
        }

        .example::before {
            content: '📝';
            position: absolute;
            top: -8px;
            left: 20px;
            background: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 1.2em;
        }

        .recommendation {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
            position: relative;
            overflow: hidden;
        }

        .recommendation::before {
            content: '💎';
            position: absolute;
            top: -8px;
            right: 20px;
            background: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 1.2em;
        }

        .action {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
            box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
            position: relative;
            overflow: hidden;
        }

        .action::before {
            content: '🚀';
            position: absolute;
            top: -8px;
            right: 20px;
            background: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 1.2em;
        }

        /* Real-life Story Boxes */
        .story {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b;
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            position: relative;
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.2);
        }

        .story::before {
            content: '👤';
            position: absolute;
            top: -8px;
            left: 20px;
            background: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 1.2em;
        }

        /* Modern Footer */
        .footer {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: white;
            padding: 50px 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
        }

        .footer-content {
            position: relative;
            z-index: 2;
        }

        .footer h3 {
            font-size: 1.5em;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .footer p {
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .company-info {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            margin-top: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        /* Print Styles */
        @media print {
            body {
                background: white;
                font-size: 12pt;
            }

            .container {
                box-shadow: none;
                border-radius: 0;
                margin: 0;
            }

            .module {
                page-break-inside: avoid;
                margin-bottom: 40px;
                box-shadow: none;
            }

            .footer {
                page-break-inside: avoid;
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 40px 20px;
            }

            .header h1 {
                font-size: 2.5em;
            }

            .content {
                padding: 40px 20px;
            }

            .module-content {
                padding: 25px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <h1>{{ content.title }}</h1>
                <h2>{{ content.subtitle }}</h2>
            </div>
        </div>

        <div class="meta-info">
            <h3>Cílová skupina:</h3>
            <div class="target-audience">
                {% for audience in content.target_audience %}
                <span class="audience-tag">{{ audience }}</span>
                {% endfor %}
            </div>
            <div class="meta-date">
                <strong>Datum:</strong> {{ content.date }}
            </div>
        </div>

        <div class="content">
            {% for module in content.modules %}
            <div class="module">
                <div class="module-header">
                    <div class="module-title">
                        <div class="module-number">{{ loop.index }}</div>
                        {{ module.title.split('.', 1)[1] if '.' in module.title else module.title }}
                    </div>
                    <div class="module-duration">{{ module.duration }}</div>
                </div>

                <div class="module-content">
                    <div class="objectives">
                        <h4>Cíle modulu:</h4>
                        <ul>
                            {% for objective in module.content.objectives %}
                            <li>{{ objective }}</li>
                            {% endfor %}
                        </ul>
                    </div>

                    {% for item in module.content.content %}
                    <div class="content-item">
                        <div class="content-title">{{ item.title }}</div>
                        <div class="content-text {{ item.type }}">
                            {{ item.text | replace('\\n', '<br>') | replace('**', '<strong>') | replace('**', '</strong>') }}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endfor %}
        </div>

        <div class="footer">
            <div class="footer-content">
                <h3>Školící materiály pro AI začátečníky</h3>
                <p>Kompletní průvodce umělou inteligencí pro rok 2025</p>
                <div class="company-info">
                    <p><strong>Vytvořila společnost Apertia Tech s.r.o.</strong></p>
                    <p>Odborné školení a konzultace v oblasti AI a digitálních technologií</p>
                    <p>© 2025 Apertia Tech s.r.o. - Všechna práva vyhrazena</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
        '''

    def generate_pdf(self, output_filename="ai-training-material-2025.pdf"):
        """Generate professional PDF training material"""
        print("🚀 Generuji kompletní školící materiál...")

        # Generate content
        content = self.generate_comprehensive_material()

        # Generate HTML
        template = Template(self.generate_html_template())
        html_content = template.render(content=content)

        # Save HTML for debugging
        html_filename = output_filename.replace('.pdf', '.html')
        with open(html_filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"✅ HTML soubor uložen: {html_filename}")

        # Generate PDF
        try:
            print("📄 Generuji PDF...")
            html_doc = HTML(string=html_content)

            # Custom CSS for better PDF formatting
            css = CSS(string='''
                @page {
                    size: A4;
                    margin: 2cm;
                }

                .module {
                    page-break-inside: avoid;
                }

                .module-header {
                    page-break-after: avoid;
                }

                h1, h2, h3, h4 {
                    page-break-after: avoid;
                }
            ''')

            html_doc.write_pdf(output_filename, stylesheets=[css], font_config=self.font_config)
            print(f"✅ PDF úspěšně vygenerováno: {output_filename}")

            # Generate summary
            self._generate_summary(content, output_filename)

            return output_filename

        except Exception as e:
            print(f"❌ Chyba při generování PDF: {e}")
            print(f"📄 HTML soubor je k dispozici: {html_filename}")
            return html_filename

    def _generate_summary(self, content, pdf_filename):
        """Generate a summary of the created material"""
        summary = f"""
📋 SOUHRN VYGENEROVANÉHO ŠKOLÍCÍHO MATERIÁLU
{'='*60}

📁 Soubor: {pdf_filename}
📅 Datum vytvoření: {content['date']}
🎯 Název: {content['title']}

📊 OBSAH:
"""

        total_duration = 0
        for i, module in enumerate(content['modules'], 1):
            duration = int(module['duration'].split()[0])
            total_duration += duration
            summary += f"   {i}. {module['title']} ({module['duration']})\n"

        summary += f"\n⏱️  Celková doba: {total_duration} minut ({total_duration//60}h {total_duration%60}min)\n"
        summary += f"👥 Cílová skupina: {', '.join(content['target_audience'])}\n"

        summary += f"""
✨ KLÍČOVÉ VLASTNOSTI:
   • Přizpůsobeno pro úplné začátečníky
   • Praktické příklady a cvičení
   • Důraz na kybernetickou bezpečnost
   • Doporučení konkrétních nástrojů
   • Profesionální design a struktura

🎯 DOPORUČENÍ PRO POUŽITÍ:
   • Materiál je připraven k okamžitému použití
   • Vhodný pro prezentaci i samostudium
   • Obsahuje interaktivní prvky pro zapojení účastníků
   • Lze rozdělit na více kratších setkání

📧 Materiál je připraven k předání vedení!
"""

        print(summary)

        # Save summary to file
        summary_filename = pdf_filename.replace('.pdf', '_summary.txt')
        with open(summary_filename, 'w', encoding='utf-8') as f:
            f.write(summary)
        print(f"📋 Souhrn uložen: {summary_filename}")

def main():
    """Main execution function"""
    print("🎓 AI TRAINING MATERIAL GENERATOR")
    print("=" * 50)

    generator = AITrainingMaterialGenerator()

    try:
        # Generate the training material
        output_file = generator.generate_pdf()

        print("\n" + "="*60)
        print("🎉 ÚSPĚCH! Školící materiál byl úspěšně vygenerován!")
        print("="*60)
        print(f"📁 Výstupní soubor: {output_file}")
        print("📋 Materiál je připraven k použití a předání vedení.")
        print("\n💡 TIP: Otevřete soubor a zkontrolujte obsah před finálním použitím.")

    except Exception as e:
        print(f"\n❌ CHYBA: {e}")
        print("🔧 Zkuste spustit skript znovu nebo kontaktujte podporu.")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
