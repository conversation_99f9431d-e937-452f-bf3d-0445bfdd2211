#!/usr/bin/env python3
"""
Professional AI Training Material Generator
Creates comprehensive training materials for AI beginners and tech laypersons
"""

import os
import sys
import json
from datetime import datetime
from jinja2 import Template
from weasyprint import HTML, CSS
from weasyprint.text.fonts import FontConfiguration
import markdown

class AITrainingMaterialGenerator:
    def __init__(self):
        self.font_config = FontConfiguration()
        
    def load_existing_content(self):
        """Load and analyze existing training materials"""
        content = {
            'cybersecurity': self._extract_html_content('ai-cybersecurity-training-2025.html'),
            'office_usage': self._extract_html_content('ai-office-training-2025.html'),
            'meeting_notes': self._extract_meeting_notes()
        }
        return content
    
    def _extract_html_content(self, filename):
        """Extract key content from HTML files"""
        if not os.path.exists(filename):
            return {}
        
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract title and key sections (simplified extraction)
        title_start = content.find('<title>') + 7
        title_end = content.find('</title>')
        title = content[title_start:title_end] if title_start > 6 else "AI Training"
        
        return {
            'title': title,
            'content': content
        }
    
    def _extract_meeting_notes(self):
        """Extract key insights from meeting transcripts"""
        notes = {}
        
        # Extract from your provided notes
        notes['key_insights'] = [
            "Online školení je náročné - nejde navázat kontakt, směřuje k monotonosti",
            "Kybernetická bezpečnost - phishing nárůst o 442%",
            "AI okopíruje už cokoliv - pozor na kopie webovek",
            "Neexistuje možnost, jak poznat AI text",
            "Rychločtení je nejlepší skill v dnešní době",
            "AI ovládá browser - tohle je budoucnost"
        ]
        
        notes['security_tips'] = [
            "Neklikat na linky/tlačítka v emailech",
            "Kontrolovat odesílatele - banky neposílají maily",
            "Otevřením se nic neděje, ale pozor na přílohy",
            "Nastavit 2-faktor ověření",
            "Dělat zálohy všude všeho",
            "Používat 16 znakové heslo"
        ]
        
        notes['ai_tools'] = [
            "Eleven Labs - asistentka",
            "NotebookLM prezentace",
            "Cerebras.ai - rychlost generování",
            "AlphaFold - využití AI ve vědě"
        ]
        
        return notes
    
    def generate_comprehensive_material(self):
        """Generate comprehensive training material"""
        
        # Load existing content
        existing_content = self.load_existing_content()
        
        # Define the comprehensive training structure
        training_content = {
            'title': 'Umělá Inteligence pro Začátečníky - Kompletní Školící Materiál 2025',
            'subtitle': 'Praktický průvodce pro uživatele bez předchozí zkušenosti s AI',
            'date': datetime.now().strftime('%d.%m.%Y'),
            'target_audience': [
                'Uživatelé bez předchozí zkušenosti s AI',
                'Technologicky méně zdatní uživatelé',
                'AI začátečníci a technologičtí laici'
            ],
            'modules': [
                {
                    'title': '1. Úvod do Umělé Inteligence',
                    'duration': '30 minut',
                    'content': self._generate_intro_module()
                },
                {
                    'title': '2. Základy Práce s AI Nástroji',
                    'duration': '45 minut',
                    'content': self._generate_basics_module()
                },
                {
                    'title': '3. Kybernetická Bezpečnost a AI',
                    'duration': '30 minut',
                    'content': self._generate_security_module(existing_content['meeting_notes'])
                },
                {
                    'title': '4. Praktické Využití AI v Kanceláři',
                    'duration': '45 minut',
                    'content': self._generate_office_module()
                },
                {
                    'title': '5. Nejlepší Nástroje a Doporučení',
                    'duration': '30 minut',
                    'content': self._generate_tools_module(existing_content['meeting_notes'])
                },
                {
                    'title': '6. Budoucnost AI a Závěr',
                    'duration': '15 minut',
                    'content': self._generate_future_module()
                }
            ]
        }
        
        return training_content
    
    def _generate_intro_module(self):
        return {
            'objectives': [
                'Pochopit, co je umělá inteligence',
                'Rozpoznat AI v každodenním životě',
                'Překonat strach z nových technologií'
            ],
            'content': [
                {
                    'type': 'text',
                    'title': 'Co je Umělá Inteligence?',
                    'text': '''
                    Umělá inteligence (AI) je technologie, která umožňuje počítačům "myslet" a řešit problémy podobně jako lidé. 
                    Není to magie - je to pokročilý software, který se naučil rozpoznávat vzory a poskytovat užitečné odpovědi.
                    
                    **Jednoduché příklady AI, které už znáte:**
                    - Doporučení na YouTube nebo Netflixu
                    - Automatické opravy textu v telefonu
                    - Navigace v mapách
                    - Rozpoznávání obličejů na fotkách
                    '''
                },
                {
                    'type': 'highlight',
                    'title': 'Klíčové poselství',
                    'text': 'AI není náhrada za lidské myšlení, ale mocný nástroj, který nám pomáhá být efektivnější.'
                }
            ]
        }
    
    def _generate_basics_module(self):
        return {
            'objectives': [
                'Naučit se základy komunikace s AI',
                'Pochopit, jak AI funguje',
                'Vyzkoušet první AI nástroj'
            ],
            'content': [
                {
                    'type': 'text',
                    'title': 'Jak komunikovat s AI',
                    'text': '''
                    **Základní pravidla pro práci s AI:**

                    1. **Buďte konkrétní** - Místo "napiš email" řekněte "napiš formální email kolegovi o schůzce"
                    2. **Používejte příklady** - "Napiš to ve stylu, jako když..."
                    3. **Iterujte** - První odpověď není finální, můžete požádat o úpravy
                    4. **Buďte trpěliví** - AI potřebuje čas na "přemýšlení"
                    '''
                },
                {
                    'type': 'exercise',
                    'title': 'Praktické cvičení',
                    'text': 'Zkuste si napsat svůj první "prompt" (požadavek) pro AI: "Vysvětli mi, jak funguje email, jako bych byl 5letý."'
                }
            ]
        }

    def _generate_security_module(self, meeting_notes):
        return {
            'objectives': [
                'Rozpoznat bezpečnostní rizika AI',
                'Naučit se bezpečné praktiky',
                'Chránit se před podvody'
            ],
            'content': [
                {
                    'type': 'warning',
                    'title': 'DŮLEŽITÉ VAROVÁNÍ',
                    'text': 'Phishing útoky vzrostly o 442%! AI umožňuje vytváření velmi přesvědčivých podvodů.'
                },
                {
                    'type': 'text',
                    'title': 'Zlatá pravidla kybernetické bezpečnosti',
                    'text': '''
                    **10 základních pravidel:**

                    1. **Neklikejte na podezřelé odkazy** v emailech
                    2. **Kontrolujte odesílatele** - banky neposílají náhodné emaily
                    3. **Otevření emailu je bezpečné**, ale pozor na přílohy a tlačítka
                    4. **Používejte 2-faktorové ověření** všude, kde je to možné
                    5. **Zálohujte vše** - pravidelně a na více míst
                    6. **Používejte silná hesla** - minimálně 16 znaků
                    7. **Buďte skeptičtí** k příliš dobrým nabídkám
                    8. **Ověřujte informace** z více zdrojů
                    9. **Aktualizujte software** pravidelně
                    10. **Důvěřujte svému instinktu** - pokud něco vypadá podezřele, pravděpodobně je
                    '''
                },
                {
                    'type': 'highlight',
                    'title': 'AI a deepfakes',
                    'text': 'AI dokáže vytvořit falešná videa a hlasy celebrit. Vždy ověřujte zdroj informací!'
                }
            ]
        }

    def _generate_office_module(self):
        return {
            'objectives': [
                'Využít AI pro každodenní úkoly',
                'Zefektivnit práci s dokumenty',
                'Automatizovat rutinní činnosti'
            ],
            'content': [
                {
                    'type': 'text',
                    'title': 'AI v kancelářské praxi',
                    'text': '''
                    **Praktické využití AI v práci:**

                    **Psaní a komunikace:**
                    - Tvorba emailů a dopisů
                    - Překlady textů
                    - Korektura a zlepšování stylu
                    - Shrnutí dlouhých dokumentů

                    **Analýza a zpracování dat:**
                    - Vytváření grafů a tabulek
                    - Analýza trendů
                    - Automatické reporty

                    **Kreativní práce:**
                    - Návrhy prezentací
                    - Tvorba obrázků a grafiky
                    - Brainstorming nápadů
                    '''
                },
                {
                    'type': 'example',
                    'title': 'Příklad použití',
                    'text': '''
                    **Úkol:** Napsat email klientovi o zpoždění dodávky

                    **Prompt pro AI:** "Napiš profesionální email klientovi, ve kterém se omluvíme za 3denní zpoždění dodávky kvůli technickým problémům. Nabídni kompenzaci a ujisti ho o kvalitě."

                    **Výsledek:** Profesionální, empatický email připravený k odeslání
                    '''
                }
            ]
        }

    def _generate_tools_module(self, meeting_notes):
        return {
            'objectives': [
                'Poznat nejlepší AI nástroje',
                'Vybrat vhodný nástroj pro úkol',
                'Začít používat AI prakticky'
            ],
            'content': [
                {
                    'type': 'text',
                    'title': 'Doporučené AI nástroje pro začátečníky',
                    'text': '''
                    **Bezplatné nástroje:**

                    1. **ChatGPT (OpenAI)** - Univerzální asistent pro text
                       - Zdarma s omezeními
                       - Nejznámější a nejpoužívanější

                    2. **Google Gemini** - Integrovaný s Google službami
                       - Zdarma měsíc na zkoušku
                       - Výborný pro vyhledávání a analýzu

                    3. **Claude (Anthropic)** - Nejbezpečnější volba
                       - Netrénuje na vašich datech
                       - Výborný pro dlouhé texty

                    4. **NotebookLM** - Vytváření prezentací a podcastů
                       - Zdarma od Googlu
                       - Umí česky
                    '''
                },
                {
                    'type': 'recommendation',
                    'title': 'Doporučení pro začátek',
                    'text': '''
                    **Začněte s jedním nástrojem** a naučte se ho dobře používat.

                    **Neplaťte nic na rok** - v době AI se vše rychle mění.

                    **Vyzkoušejte zdarma verze** před nákupem placených verzí.
                    '''
                }
            ]
        }

    def _generate_future_module(self):
        return {
            'objectives': [
                'Pochopit trendy v AI',
                'Připravit se na budoucnost',
                'Pokračovat v učení'
            ],
            'content': [
                {
                    'type': 'text',
                    'title': 'Budoucnost AI',
                    'text': '''
                    **Co nás čeká:**

                    - **AI agenti** - automatizace celých pracovních procesů
                    - **Hlasové asistenty** - nerozpoznatelní od lidí
                    - **Personalizované AI** - přizpůsobené vašim potřebám
                    - **AI v prohlížečích** - automatické vyplňování formulářů

                    **Nejdůležitější dovednost:** Rychločtení a ověřování informací
                    '''
                },
                {
                    'type': 'action',
                    'title': 'Vaše další kroky',
                    'text': '''
                    1. **Vyberte si jeden AI nástroj** a používejte ho týden
                    2. **Experimentujte** s různými typy úkolů
                    3. **Sdílejte zkušenosti** s kolegy
                    4. **Sledujte novinky** - AI se vyvíjí velmi rychle
                    5. **Buďte trpěliví** - učení nových technologií chce čas
                    '''
                }
            ]
        }

    def generate_html_template(self):
        """Generate professional HTML template"""
        return '''
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ content.title }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header h2 {
            font-size: 1.2em;
            opacity: 0.9;
            font-weight: 300;
        }

        .meta-info {
            background: #f8f9fa;
            padding: 20px 40px;
            border-left: 4px solid #3498db;
        }

        .meta-info h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .target-audience {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .audience-tag {
            background: #3498db;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
        }

        .content {
            padding: 40px;
        }

        .module {
            margin-bottom: 50px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }

        .module-header {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 20px;
        }

        .module-title {
            font-size: 1.5em;
            margin-bottom: 5px;
        }

        .module-duration {
            opacity: 0.8;
            font-size: 0.9em;
        }

        .module-content {
            padding: 30px;
        }

        .objectives {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 25px;
        }

        .objectives h4 {
            color: #27ae60;
            margin-bottom: 10px;
        }

        .objectives ul {
            list-style: none;
            padding-left: 0;
        }

        .objectives li {
            padding: 5px 0;
            position: relative;
            padding-left: 25px;
        }

        .objectives li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #27ae60;
            font-weight: bold;
        }

        .content-item {
            margin-bottom: 25px;
        }

        .content-title {
            font-size: 1.2em;
            color: #2c3e50;
            margin-bottom: 15px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }

        .content-text {
            line-height: 1.8;
        }

        .content-text strong {
            color: #2c3e50;
        }

        .highlight {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .warning {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .exercise {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .example {
            background: #f8f9fa;
            border-left: 4px solid #17a2b8;
            padding: 20px;
            margin: 20px 0;
        }

        .recommendation {
            background: linear-gradient(135deg, #1abc9c 0%, #16a085 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .action {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .footer {
            background: #2c3e50;
            color: white;
            padding: 30px;
            text-align: center;
        }

        @media print {
            body {
                background: white;
            }

            .container {
                box-shadow: none;
            }

            .module {
                page-break-inside: avoid;
                margin-bottom: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ content.title }}</h1>
            <h2>{{ content.subtitle }}</h2>
        </div>

        <div class="meta-info">
            <h3>Cílová skupina:</h3>
            <div class="target-audience">
                {% for audience in content.target_audience %}
                <span class="audience-tag">{{ audience }}</span>
                {% endfor %}
            </div>
            <p style="margin-top: 15px;"><strong>Datum:</strong> {{ content.date }}</p>
        </div>

        <div class="content">
            {% for module in content.modules %}
            <div class="module">
                <div class="module-header">
                    <div class="module-title">{{ module.title }}</div>
                    <div class="module-duration">Doba trvání: {{ module.duration }}</div>
                </div>

                <div class="module-content">
                    <div class="objectives">
                        <h4>Cíle modulu:</h4>
                        <ul>
                            {% for objective in module.content.objectives %}
                            <li>{{ objective }}</li>
                            {% endfor %}
                        </ul>
                    </div>

                    {% for item in module.content.content %}
                    <div class="content-item">
                        <div class="content-title">{{ item.title }}</div>
                        <div class="content-text {{ item.type }}">
                            {{ item.text | replace('\\n', '<br>') | replace('**', '<strong>') | replace('**', '</strong>') }}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endfor %}
        </div>

        <div class="footer">
            <p>© 2025 - Školící materiály pro AI začátečníky</p>
            <p>Vytvořeno s pomocí umělé inteligence</p>
        </div>
    </div>
</body>
</html>
        '''

    def generate_pdf(self, output_filename="ai-training-material-2025.pdf"):
        """Generate professional PDF training material"""
        print("🚀 Generuji kompletní školící materiál...")

        # Generate content
        content = self.generate_comprehensive_material()

        # Generate HTML
        template = Template(self.generate_html_template())
        html_content = template.render(content=content)

        # Save HTML for debugging
        html_filename = output_filename.replace('.pdf', '.html')
        with open(html_filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"✅ HTML soubor uložen: {html_filename}")

        # Generate PDF
        try:
            print("📄 Generuji PDF...")
            html_doc = HTML(string=html_content)

            # Custom CSS for better PDF formatting
            css = CSS(string='''
                @page {
                    size: A4;
                    margin: 2cm;
                }

                .module {
                    page-break-inside: avoid;
                }

                .module-header {
                    page-break-after: avoid;
                }

                h1, h2, h3, h4 {
                    page-break-after: avoid;
                }
            ''')

            html_doc.write_pdf(output_filename, stylesheets=[css], font_config=self.font_config)
            print(f"✅ PDF úspěšně vygenerováno: {output_filename}")

            # Generate summary
            self._generate_summary(content, output_filename)

            return output_filename

        except Exception as e:
            print(f"❌ Chyba při generování PDF: {e}")
            print(f"📄 HTML soubor je k dispozici: {html_filename}")
            return html_filename

    def _generate_summary(self, content, pdf_filename):
        """Generate a summary of the created material"""
        summary = f"""
📋 SOUHRN VYGENEROVANÉHO ŠKOLÍCÍHO MATERIÁLU
{'='*60}

📁 Soubor: {pdf_filename}
📅 Datum vytvoření: {content['date']}
🎯 Název: {content['title']}

📊 OBSAH:
"""

        total_duration = 0
        for i, module in enumerate(content['modules'], 1):
            duration = int(module['duration'].split()[0])
            total_duration += duration
            summary += f"   {i}. {module['title']} ({module['duration']})\n"

        summary += f"\n⏱️  Celková doba: {total_duration} minut ({total_duration//60}h {total_duration%60}min)\n"
        summary += f"👥 Cílová skupina: {', '.join(content['target_audience'])}\n"

        summary += f"""
✨ KLÍČOVÉ VLASTNOSTI:
   • Přizpůsobeno pro úplné začátečníky
   • Praktické příklady a cvičení
   • Důraz na kybernetickou bezpečnost
   • Doporučení konkrétních nástrojů
   • Profesionální design a struktura

🎯 DOPORUČENÍ PRO POUŽITÍ:
   • Materiál je připraven k okamžitému použití
   • Vhodný pro prezentaci i samostudium
   • Obsahuje interaktivní prvky pro zapojení účastníků
   • Lze rozdělit na více kratších setkání

📧 Materiál je připraven k předání vedení!
"""

        print(summary)

        # Save summary to file
        summary_filename = pdf_filename.replace('.pdf', '_summary.txt')
        with open(summary_filename, 'w', encoding='utf-8') as f:
            f.write(summary)
        print(f"📋 Souhrn uložen: {summary_filename}")

def main():
    """Main execution function"""
    print("🎓 AI TRAINING MATERIAL GENERATOR")
    print("=" * 50)

    generator = AITrainingMaterialGenerator()

    try:
        # Generate the training material
        output_file = generator.generate_pdf()

        print("\n" + "="*60)
        print("🎉 ÚSPĚCH! Školící materiál byl úspěšně vygenerován!")
        print("="*60)
        print(f"📁 Výstupní soubor: {output_file}")
        print("📋 Materiál je připraven k použití a předání vedení.")
        print("\n💡 TIP: Otevřete soubor a zkontrolujte obsah před finálním použitím.")

    except Exception as e:
        print(f"\n❌ CHYBA: {e}")
        print("🔧 Zkuste spustit skript znovu nebo kontaktujte podporu.")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
