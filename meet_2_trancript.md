{"segments": [{"StartTime": 120000, "EndTime": 120000, "VoiceStart": 120000, "VoiceEnd": 120000, "Speaker": "Automated Voice", "text": "You are currently the only person in this conference."}, {"StartTime": 267000, "EndTime": 267000, "VoiceStart": 267000, "VoiceEnd": 267000, "Speaker": "Speaker 1", "text": "Do you have any questions about this before I send it out?"}, {"StartTime": 269000, "EndTime": 269000, "VoiceStart": 269000, "VoiceEnd": 269000, "Speaker": "Speaker 2", "text": "I do, actually."}, {"StartTime": 270000, "EndTime": 270000, "VoiceStart": 270000, "VoiceEnd": 270000, "Speaker": "Speaker 1", "text": "Go ahead."}, {"StartTime": 271000, "EndTime": 271000, "VoiceStart": 271000, "VoiceEnd": 271000, "Speaker": "Speaker 2", "text": "Um, so I see this whole section here about project kickoff with the client next week. And then the next section is building out milestones and resource allocation. I'm just curious about the timing for that because it seems like we might be putting the cart before the horse by starting the milestone building before we even kick off with the client next week."}, {"StartTime": 291000, "EndTime": 291000, "VoiceStart": 291000, "VoiceEnd": 291000, "Speaker": "Speaker 1", "text": "That's a fair point. So, in our previous discussions, we had decided that the kick off meeting with the client would be focused more on aligning on the high-level goals and expectations and getting their input on the overall vision. We wanted to make sure that we had a preliminary structure for the project milestones and resource allocation ready to discuss with them during that meeting. This way, we can get their immediate feedback on a tangible plan and make adjustments in real-time."}, {"StartTime": 321000, "EndTime": 321000, "VoiceStart": 321000, "VoiceEnd": 321000, "Speaker": "Speaker 2", "text": "Okay, I understand the rationale behind having something prepared. My concern is more about making potentially significant changes to the milestones and resource allocation after we've already done a lot of the detailed planning internally. What if their feedback during the kickoff requires us to completely rework the plan?"}, {"StartTime": 344000, "EndTime": 344000, "VoiceStart": 344000, "VoiceEnd": 344000, "Speaker": "Speaker 1", "text": "That's a valid concern. The intention here is to have a draft plan ready. We are not presenting this as a final, locked-in document. We'll be transparent with the client that this is our initial proposed structure based on our understanding of the project scope, and we are looking for their input and alignment. The goal is to minimize rework by getting their buy-in early on a proposed framework, rather than starting from scratch after the kickoff."}, {"StartTime": 359000, "EndTime": 359000, "VoiceStart": 359000, "VoiceEnd": 359000, "Speaker": "Speaker 2", "text": "Okay, that makes more sense if it's presented as a draft for discussion. I just wanted to make sure we weren't locked into a detailed plan before getting that initial client feedback."}, {"StartTime": 359000, "EndTime": 359000, "VoiceStart": 359000, "VoiceEnd": 359000, "Speaker": "Speaker 1", "text": "Exactly. It's about having a starting point for a productive conversation, not presenting them with a finished product they had no input on."}, {"StartTime": 386000, "EndTime": 386000, "VoiceStart": 386000, "VoiceEnd": 386000, "Speaker": "Speaker 1", "text": "that will uh make it really easy for you to see what you've done on each student."}, {"StartTime": 389000, "EndTime": 389000, "VoiceStart": 389000, "VoiceEnd": 389000, "Speaker": "Speaker 2", "text": "Okay."}, {"StartTime": 390000, "EndTime": 390000, "VoiceStart": 390000, "VoiceEnd": 390000, "Speaker": "Speaker 1", "text": "Okay. So what I was going to tell you, um, since, uh, I was out yesterday too, uh, I I did see that your email came across yesterday."}, {"StartTime": 400000, "EndTime": 400000, "VoiceStart": 400000, "VoiceEnd": 400000, "Speaker": "Speaker 2", "text": "Okay."}, {"StartTime": 400000, "EndTime": 400000, "VoiceStart": 400000, "VoiceEnd": 400000, "Speaker": "Speaker 1", "text": "Um, and, uh, I I did respond, uh, to it this morning, uh, with, uh, I I wasn't sure you said you were stuck and I wasn't sure exactly what you were stuck on. Okay."}, {"StartTime": 410000, "EndTime": 410000, "VoiceStart": 410000, "VoiceEnd": 410000, "Speaker": "Speaker 2", "text": "Okay. Um, I'm actually creating. Okay. I am sharing my screen. You can see my screen?"}, {"StartTime": 417000, "EndTime": 417000, "VoiceStart": 417000, "VoiceEnd": 417000, "Speaker": "Speaker 1", "text": "Yes, I can."}, {"StartTime": 418000, "EndTime": 418000, "VoiceStart": 418000, "VoiceEnd": 418000, "Speaker": "Speaker 2", "text": "Okay. I am in a student account that I created that I wanted to work on because it was one student that I didn't have a file for and I just needed to create a file for him just to see what it looked like. So I'm like, okay, well, I'll go ahead and create a student to work on. Okay. And that's why I went to add. So let's let's back out a little bit. Okay."}, {"StartTime": 441000, "EndTime": 441000, "VoiceStart": 441000, "VoiceEnd": 441000, "Speaker": "Speaker 1", "text": "Okay."}, {"StartTime": 442000, "EndTime": 442000, "VoiceStart": 442000, "VoiceEnd": 442000, "Speaker": "Speaker 2", "text": "So I'm just on the main screen, so I just want to get it where it says new student. Okay. So, let me see if I go to find student. Let me see if it shows up there. Student. And I know his name is like, uh, <PERSON><PERSON>, I think."}, {"StartTime": 460000, "EndTime": 460000, "VoiceStart": 460000, "VoiceEnd": 460000, "Speaker": "Speaker 1", "text": "Okay. And so this would be your student you created, is that right?"}, {"StartTime": 464000, "EndTime": 464000, "VoiceStart": 464000, "VoiceEnd": 464000, "Speaker": "Speaker 2", "text": "Yeah. This is the student that I created that I wanted to work on to see if, uh, if I can create a whole file and it be correct."}, {"StartTime": 474000, "EndTime": 474000, "VoiceStart": 474000, "VoiceEnd": 474000, "Speaker": "Speaker 1", "text": "Okay. All right."}, {"StartTime": 476000, "EndTime": 476000, "VoiceStart": 476000, "VoiceEnd": 476000, "Speaker": "Speaker 2", "text": "So then I went to new student. I didn't go to find student. I went to new student."}, {"StartTime": 488000, "EndTime": 488000, "VoiceStart": 488000, "VoiceEnd": 488000, "Speaker": "Speaker 1", "text": "Okay."}, {"StartTime": 490000, "EndTime": 490000, "VoiceStart": 490000, "VoiceEnd": 490000, "Speaker": "Speaker 2", "text": "Um, so that was kind of the first step, you know, the big one that we've been working on since January is doing uh the re um rewrite of the website with our partners, uh Big Sea."}, {"StartTime": 505000, "EndTime": 505000, "VoiceStart": 505000, "VoiceEnd": 505000, "Speaker": "Speaker 1", "text": "Okay. And where is that at in terms of percentage complete?"}, {"StartTime": 510000, "EndTime": 510000, "VoiceStart": 510000, "VoiceEnd": 510000, "Speaker": "Speaker 2", "text": "Um, so they are still working on the build. Um, it's kind of in that testing phase. I'd say they're probably about 90, 95% done. Uh and then they have like two weeks of like final review and bug fixing that we're getting into right now. So hopefully launching in the next couple of weeks."}, {"StartTime": 534000, "EndTime": 534000, "VoiceStart": 534000, "VoiceEnd": 534000, "Speaker": "Speaker 1", "text": "Okay. Any challenges or obstacles there?"}, {"StartTime": 537000, "EndTime": 537000, "VoiceStart": 537000, "VoiceEnd": 537000, "Speaker": "Speaker 2", "text": "Um, the biggest challenge was uh kind of the transition over because uh when we started working with them, uh we were also bringing on a new full-time hire. So the person who previously oversaw the website, um, kind of transitioned off right as we started the new website build. So, um, kind of getting, uh, you know, my hire up to speed, getting, um, you know, big sea familiar with our infrastructure. That was definitely the biggest challenge. Um, I think now though, you know, we're kind of in the home stretch, so it's a little bit smoother sailing."}, {"StartTime": 567000, "EndTime": 567000, "VoiceStart": 567000, "VoiceEnd": 567000, "Speaker": "Speaker 1", "text": "Okay. And I guess this is just kind of a general question. Have you gotten any indication that the new website is user-friendly? Or is that something that you won't know until it's up and running?"}, {"StartTime": 583000, "EndTime": 583000, "VoiceStart": 583000, "VoiceEnd": 583000, "Speaker": "Speaker 2", "text": "Yeah, I mean, so we've been having kind of the opportunities uh for review and testing. Um, so we've gotten some feedback from uh people on the team, um, and even some external review. So, um, we're feeling pretty good about it. I think it is a lot more intuitive, easier to navigate. So, uh I'm optimistic."}, {"StartTime": 624000, "EndTime": 624000, "VoiceStart": 624000, "VoiceEnd": 624000, "Speaker": "Speaker 1", "text": "se to jako č<PERSON>, jako to je čistě o tom jako čistá technická podpora."}, {"StartTime": 630000, "EndTime": 630000, "VoiceStart": 630000, "VoiceEnd": 630000, "Speaker": "Speaker 2", "text": "<PERSON>, je to <PERSON><PERSON><PERSON>, č<PERSON><PERSON> o tom jako technick<PERSON>"}, {"StartTime": 635000, "EndTime": 635000, "VoiceStart": 635000, "VoiceEnd": 635000, "Speaker": "Speaker 1", "text": "technická podpora. Takže ty jakoby počítače potřebuje"}, {"StartTime": 641000, "EndTime": 641000, "VoiceStart": 641000, "VoiceEnd": 641000, "Speaker": "Speaker 2", "text": "potřebuješ."}, {"StartTime": 645000, "EndTime": 645000, "VoiceStart": 645000, "VoiceEnd": 645000, "Speaker": "Speaker 1", "text": "Hm."}, {"StartTime": 655000, "EndTime": 655000, "VoiceStart": 655000, "VoiceEnd": 655000, "Speaker": "Speaker 3", "text": "<PERSON><PERSON> byla taky na tom hlavním čase. <PERSON><PERSON>, já budu jenom."}, {"StartTime": 659000, "EndTime": 659000, "VoiceStart": 659000, "VoiceEnd": 659000, "Speaker": "Speaker 4", "text": "<PERSON><PERSON><PERSON>."}, {"StartTime": 660000, "EndTime": 660000, "VoiceStart": 660000, "VoiceEnd": 660000, "Speaker": "Speaker 5", "text": "<PERSON><PERSON><PERSON>."}, {"StartTime": 662000, "EndTime": 662000, "VoiceStart": 662000, "VoiceEnd": 662000, "Speaker": "Speaker 4", "text": "<PERSON><PERSON>."}, {"StartTime": 664000, "EndTime": 664000, "VoiceStart": 664000, "VoiceEnd": 664000, "Speaker": "Speaker 6", "text": "Dobrý den."}, {"StartTime": 665000, "EndTime": 665000, "VoiceStart": 665000, "VoiceEnd": 665000, "Speaker": "Speaker 5", "text": "<PERSON>, za za<PERSON><PERSON><PERSON><PERSON>, j<PERSON> to j<PERSON> to zkusím. Jo."}, {"StartTime": 671000, "EndTime": 671000, "VoiceStart": 671000, "VoiceEnd": 671000, "Speaker": "Speaker 5", "text": "Byla to penalováno v pohodě? Bylo. Nebo na? Bylo. Jo?"}, {"StartTime": 677000, "EndTime": 677000, "VoiceStart": 677000, "VoiceEnd": 677000, "Speaker": "Speaker 5", "text": "To je an<PERSON>n<PERSON>."}, {"StartTime": 680000, "EndTime": 680000, "VoiceStart": 680000, "VoiceEnd": 680000, "Speaker": "Speaker 3", "text": "<PERSON><PERSON> si jo, jo, jo taky d<PERSON>."}, {"StartTime": 681000, "EndTime": 681000, "VoiceStart": 681000, "VoiceEnd": 681000, "Speaker": "Speaker 5", "text": "<PERSON>."}, {"StartTime": 683000, "EndTime": 683000, "VoiceStart": 683000, "VoiceEnd": 683000, "Speaker": "Speaker 3", "text": "<PERSON>, taky dě<PERSON><PERSON>."}, {"StartTime": 685000, "EndTime": 685000, "VoiceStart": 685000, "VoiceEnd": 685000, "Speaker": "Speaker 5", "text": "Tak jo. Jo."}, {"StartTime": 699000, "EndTime": 699000, "VoiceStart": 699000, "VoiceEnd": 699000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON><PERSON><PERSON> den, <PERSON><PERSON><PERSON><PERSON> se?"}, {"StartTime": 701000, "EndTime": 701000, "VoiceStart": 701000, "VoiceEnd": 701000, "Speaker": "Speaker 6", "text": "Dobrý den."}, {"StartTime": 702000, "EndTime": 702000, "VoiceStart": 702000, "VoiceEnd": 702000, "Speaker": "Speaker 5", "text": "Dobrý den."}, {"StartTime": 703000, "EndTime": 703000, "VoiceStart": 703000, "VoiceEnd": 703000, "Speaker": "Speaker 5", "text": "<PERSON><PERSON>, dneska pan eh Holub má eh důležité setkání, tak<PERSON><PERSON> víceméně eh, p<PERSON><PERSON><PERSON><PERSON><PERSON>, já vám to předpokládám. A předpokládám. Tak já jsem tady víceméně jako"}, {"StartTime": 720000, "EndTime": 720000, "VoiceStart": 720000, "VoiceEnd": 720000, "Speaker": "Speaker 1", "text": "jakoby zástupce a eh tady v zasedačce budeme samozřejmě i online a můžeme navázat na to, co jsme tady řešili minule."}, {"StartTime": 730000, "EndTime": 730000, "VoiceStart": 730000, "VoiceEnd": 730000, "Speaker": "Speaker 2", "text": "<PERSON><PERSON><PERSON><PERSON>."}, {"StartTime": 732000, "EndTime": 732000, "VoiceStart": 732000, "VoiceEnd": 732000, "Speaker": "Speaker 2", "text": "Tak za vás máte k tomu minule jakékoliv dotazy?"}, {"StartTime": 737000, "EndTime": 737000, "VoiceStart": 737000, "VoiceEnd": 737000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON> ještě asi bych p<PERSON>, jestli se přip<PERSON><PERSON><PERSON> v<PERSON>. <PERSON><PERSON>. To je ještě, jestli to u<PERSON><PERSON><PERSON><PERSON>, jestli to bude vadit."}, {"StartTime": 745000, "EndTime": 745000, "VoiceStart": 745000, "VoiceEnd": 745000, "Speaker": "Speaker 2", "text": "To je spr<PERSON>vn<PERSON>, tak to má bejt."}, {"StartTime": 752000, "EndTime": 752000, "VoiceStart": 752000, "VoiceEnd": 752000, "Speaker": "Speaker 3", "text": "<PERSON><PERSON>, ne, já jsem si to ulož<PERSON>. Může se stíha jako ne."}, {"StartTime": 759000, "EndTime": 759000, "VoiceStart": 759000, "VoiceEnd": 759000, "Speaker": "Speaker 3", "text": "<PERSON><PERSON><PERSON><PERSON> tady právě k Inteligence, kde jsem se vyk<PERSON>p."}, {"StartTime": 762000, "EndTime": 762000, "VoiceStart": 762000, "VoiceEnd": 762000, "Speaker": "Speaker 3", "text": "<PERSON><PERSON> <PERSON><PERSON>ch se je<PERSON><PERSON><PERSON> z<PERSON> te<PERSON>, ne<PERSON>, j<PERSON><PERSON> vlastně na takovou praktickou věc, k<PERSON><PERSON> jsem to asi nebude zajímat ostatní, proto<PERSON>e jsem tím prošla jenom já."}, {"StartTime": 771000, "EndTime": 771000, "VoiceStart": 771000, "VoiceEnd": 771000, "Speaker": "Speaker 2", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>."}, {"StartTime": 772000, "EndTime": 772000, "VoiceStart": 772000, "VoiceEnd": 772000, "Speaker": "Speaker 3", "text": "Chtěla jsem se zeptat, j<PERSON>, já mám na starosti marketing u nás a samozřejmě teda hodně vždycky pracuju a jen tak, protože abych se jak<PERSON>y zdokonalila sama, tak jsem si prostě našla nějakej prostě kurz od jenom takový školení dvouhodinový od Edith AI."}, {"StartTime": 789000, "EndTime": 789000, "VoiceStart": 789000, "VoiceEnd": 789000, "Speaker": "Speaker 3", "text": "<PERSON>e samozřej<PERSON>ě to mě<PERSON> za za následek, jako<PERSON><PERSON> prostě bych si potřebovala koupit tu jejich aplikaci, která zahrnuje všechny možný umělý inteligence na všechno možný a má to prostě nějak<PERSON>, je to takovej ten osobní asistent v rámci nějaký aplikace jejich, kter<PERSON> je placená."}, {"StartTime": 805000, "EndTime": 805000, "VoiceStart": 805000, "VoiceEnd": 805000, "Speaker": "Speaker 3", "text": "<PERSON>k jsem se ch<PERSON><PERSON><PERSON>, jestli tady to znáte a nebo jestli jsem to správně pochopila a jestli to znáte tady tu <PERSON>."}, {"StartTime": 812000, "EndTime": 812000, "VoiceStart": 812000, "VoiceEnd": 812000, "Speaker": "Speaker 3", "text": "<PERSON><PERSON> že to je vlastně jenom taková jakoby forma toho, co bych mohla si mít každý z<PERSON>l<PERSON>, a<PERSON><PERSON><PERSON> že je to na jednom místě všechno dohromady."}, {"StartTime": 821000, "EndTime": 821000, "VoiceStart": 821000, "VoiceEnd": 821000, "Speaker": "Speaker 1", "text": "Pokud <PERSON><PERSON> to z<PERSON><PERSON>, ale my to ne<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jo. My máme všechny placený od zdroje, řekněme, to znamená, všechny platíme přímo u u Googlu, u Antropiku, u Open AI, eh Groka máme. Máme prostě všechny, takže pro nás to nepřináší tu výhodu."}, {"StartTime": 840000, "EndTime": 840000, "VoiceStart": 840000, "VoiceEnd": 840000, "Speaker": "Speaker 1", "text": "tam <PERSON>, jest<PERSON> to pro vás ekonomicky a lidsky dává s<PERSON>l, tak to asi není prob<PERSON>m."}, {"StartTime": 845000, "EndTime": 845000, "VoiceStart": 845000, "VoiceEnd": 845000, "Speaker": "Speaker 1", "text": "<PERSON>."}, {"StartTime": 846000, "EndTime": 846000, "VoiceStart": 846000, "VoiceEnd": 846000, "Speaker": "Speaker 2", "text": "Mhm, mhm."}, {"StartTime": 847000, "EndTime": 847000, "VoiceStart": 847000, "VoiceEnd": 847000, "Speaker": "Speaker 1", "text": "<PERSON>."}, {"StartTime": 848000, "EndTime": 848000, "VoiceStart": 848000, "VoiceEnd": 848000, "Speaker": "Speaker 2", "text": "<PERSON><PERSON> jsem ch<PERSON><PERSON><PERSON> v<PERSON>, jest<PERSON> je v tom jakoby nějaká, nějak<PERSON> výhoda toho jakoby."}, {"StartTime": 852000, "EndTime": 852000, "VoiceStart": 852000, "VoiceEnd": 852000, "Speaker": "Speaker 1", "text": "Tak jediná výhoda je to, že to pravděpodobně máte na jednom místě."}, {"StartTime": 857000, "EndTime": 857000, "VoiceStart": 857000, "VoiceEnd": 857000, "Speaker": "Speaker 1", "text": "<PERSON>, tam."}, {"StartTime": 859000, "EndTime": 859000, "VoiceStart": 859000, "VoiceEnd": 859000, "Speaker": "Speaker 2", "text": "Jenom si přecvakávat různý různý."}, {"StartTime": 861000, "EndTime": 861000, "VoiceStart": 861000, "VoiceEnd": 861000, "Speaker": "Speaker 1", "text": "No. a máte to za jednu cenu, jo?"}, {"StartTime": 864000, "EndTime": 864000, "VoiceStart": 864000, "VoiceEnd": 864000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON>, j<PERSON> neví<PERSON>, co oni v tom všechno nabízejí. Co tam, co tam mají."}, {"StartTime": 869000, "EndTime": 869000, "VoiceStart": 869000, "VoiceEnd": 869000, "Speaker": "Speaker 2", "text": "No, tak oni tam jakoby oni to prezentovali tak, že já si tam vlastně vytvořím svůj vlastní profil jakože kdo jsem a prostě to, o čem jsme se bavili minule, že si tam vlastně nacvakám úplně ty všechny informace o sobě, co jak potřebuju, co jak hledám, co jsem za člověka, že dělám marketing a tak a potom ono si to vlastně už pamatuje a veškerou tu práci, kterou dělám už přizpůsobuje vlastně na mě. Jo, ale vytváří to obr<PERSON><PERSON><PERSON>, videa, prezentace, eh publikace na web, publikace prostě na sociální sítě a podobně."}, {"StartTime": 902000, "EndTime": 902000, "VoiceStart": 902000, "VoiceEnd": 902000, "Speaker": "Speaker 2", "text": "A samozřejmě si tam v<PERSON>, jest<PERSON> ch<PERSON> p<PERSON>, eh, jak"}, {"StartTime": 910000, "EndTime": 910000, "VoiceStart": 910000, "VoiceEnd": 910000, "Speaker": "Speaker 1", "text": "to."}, {"StartTime": 911000, "EndTime": 911000, "VoiceStart": 911000, "VoiceEnd": 911000, "Speaker": "Speaker 2", "text": "Te<PERSON> jsem se asi tady v<PERSON>, že na chvíli?"}, {"StartTime": 913000, "EndTime": 913000, "VoiceStart": 913000, "VoiceEnd": 913000, "Speaker": "Speaker 1", "text": "Jo, ale."}, {"StartTime": 915000, "EndTime": 915000, "VoiceStart": 915000, "VoiceEnd": 915000, "Speaker": "Speaker 1", "text": "Ale ta cena je trošičku úplně jako mimo, si myslím."}, {"StartTime": 918000, "EndTime": 918000, "VoiceStart": 918000, "VoiceEnd": 918000, "Speaker": "Speaker 2", "text": "No, ale ono to<PERSON>, k<PERSON><PERSON> si projedete tím školením, tak vlastně tady tu nabídku na rok nabízejí za 15 000 Kč."}, {"StartTime": 925000, "EndTime": 925000, "VoiceStart": 925000, "VoiceEnd": 925000, "Speaker": "Speaker 1", "text": "<PERSON>, to je stejn<PERSON>ě mimo."}, {"StartTime": 927000, "EndTime": 927000, "VoiceStart": 927000, "VoiceEnd": 927000, "Speaker": "Speaker 2", "text": "<PERSON>."}, {"StartTime": 929000, "EndTime": 929000, "VoiceStart": 929000, "VoiceEnd": 929000, "Speaker": "Speaker 1", "text": "To je jakoby."}, {"StartTime": 930000, "EndTime": 930000, "VoiceStart": 930000, "VoiceEnd": 930000, "Speaker": "Speaker 2", "text": "<PERSON><PERSON> to placen<PERSON> tý firmy."}, {"StartTime": 933000, "EndTime": 933000, "VoiceStart": 933000, "VoiceEnd": 933000, "Speaker": "Speaker 1", "text": "No, je to <PERSON>plně nesmyslná cena oproti tomu, <PERSON><PERSON><PERSON> m<PERSON><PERSON><PERSON><PERSON><PERSON> clot stoj<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, eh, 300 Kč."}, {"StartTime": 944000, "EndTime": 944000, "VoiceStart": 944000, "VoiceEnd": 944000, "Speaker": "Speaker 1", "text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON> k<PERSON> 12 je 3 600."}, {"StartTime": 947000, "EndTime": 947000, "VoiceStart": 947000, "VoiceEnd": 947000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON><PERSON> je úpln<PERSON> jako b<PERSON>, ř<PERSON><PERSON><PERSON><PERSON>."}, {"StartTime": 951000, "EndTime": 951000, "VoiceStart": 951000, "VoiceEnd": 951000, "Speaker": "Speaker 1", "text": "A 50 000 slov, to to j<PERSON><PERSON>, co tady mají je za 2,5000. To znamená za 30 000 ročně."}, {"StartTime": 960000, "EndTime": 960000, "VoiceStart": 960000, "VoiceEnd": 960000, "Speaker": "Speaker 1", "text": "a to je o<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> to <PERSON><PERSON><PERSON> zjednodušeně. Jo, to je."}, {"StartTime": 965000, "EndTime": 965000, "VoiceStart": 965000, "VoiceEnd": 965000, "Speaker": "Speaker 2", "text": "<PERSON><PERSON><PERSON><PERSON>, tak to jsem tak n<PERSON>jak, já jsem to z toho si tak myslela, tak jsem to jakoby si chtěla jakoby pot<PERSON>, že to, že to je asi úpln<PERSON> z<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, protože v podstatě, k<PERSON><PERSON> si dám někde bokem nějakou informaci o mně a dám to do toho jenom do toho chatu a řeknu, a teď chci todle, tak to vlastn<PERSON> bude pracovat úplně stejně."}, {"StartTime": 981000, "EndTime": 981000, "VoiceStart": 981000, "VoiceEnd": 981000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON> a <PERSON>, to my<PERSON><PERSON><PERSON><PERSON>, oni určitě na tom všechny ty firmy dělají. Dokonce a to se můžeme k tomu dostat. <PERSON><PERSON> nev<PERSON>, jestli to tady m<PERSON>, jestli už jsou tady všichni. Takže klidně můžeme začít."}, {"StartTime": 996000, "EndTime": 996000, "VoiceStart": 996000, "VoiceEnd": 996000, "Speaker": "Speaker 2", "text": "<PERSON><PERSON><PERSON><PERSON>, tak děkuji zatím a budu poslouchat pozorně, no."}, {"StartTime": 998000, "EndTime": 998000, "VoiceStart": 998000, "VoiceEnd": 998000, "Speaker": "Speaker 1", "text": "Ale to na to nav<PERSON><PERSON><PERSON>, jo? To je, to je docela takovej taková k<PERSON> informace. V době AI nic neplaťte na rok, ani ani jako vůbec. na měsíc je super, že si to můžete na pět dnů vyzkouš<PERSON>. Jak jsem ř<PERSON>al, Google vám dává za 500 Kč měsíčně Google eh, to je Google One, myslím nebo Google Gemini trial, jestli se nepletu, budete to zase případně mít v tý prezentaci, k<PERSON>ž dáte Gemini a já kdybych se odhlásil, tak oni dávají na měsíc zdarma něco, co je potom za 500. <PERSON> a to je, tam je ta nej, za mě nejvyšší přidaná hodnota, protože je to, jestli máte Androidový telefony, tak je to úplně skvěle integrovaný s telefonem, jako<PERSON><PERSON> ono to jde i na <PERSON> sa<PERSON>, ten jejich <PERSON>, ale s <PERSON>em, proto<PERSON>e to je od Googlu a Android, tak si to rozum<PERSON> hodně dobře. A jak jsem vám to tady minule ukazoval, že normálně ten telefon vezmete. Já jsem to ukazoval svojí 70letý mámě, která má ráda kytky. Přijdu, zapnu tomu kameru a chodím po zahradě a říkám, co je tohle za strom, co je tady todlecto za kytku a on vám to v reálným čase. Máma si teda stěžovala, že má vteřinu zpoždění, skutečně jako jednu vteřinu. Tak vám říká, co to je, kdo to je, jak."}, {"StartTime": 1080000, "EndTime": 1080000, "VoiceStart": 1080000, "VoiceEnd": 1080000, "Speaker": "Speaker 1", "text": "opravit pračku a tak, jo? To tam teď jsme. Oni udělali i Google prezentaci."}, {"StartTime": 1086000, "EndTime": 1086000, "VoiceStart": 1086000, "VoiceEnd": 1086000, "Speaker": "Speaker 1", "text": "<PERSON>o je to všechno spojený s tou AI, i k<PERSON>ž jsme u kybernetický bezpečnosti, tak já na to plynule navážu."}, {"StartTime": 1091000, "EndTime": 1091000, "VoiceStart": 1091000, "VoiceEnd": 1091000, "Speaker": "Speaker 1", "text": "Co to vlastně zname<PERSON> a jaký to má vliv i na kybernetickou bezpečnost, to co vlastně se teď děje ve světě AI."}, {"StartTime": 1099000, "EndTime": 1099000, "VoiceStart": 1099000, "VoiceEnd": 1099000, "Speaker": "Speaker 1", "text": "A práv<PERSON>, že za 500 Kč a ještě si to můžete na měsíc zkusit, tak ještě dostanete dokonce 2 TB úložiště pro svoje fotky a to můžete sdílet s rodinou. My to tak máme."}, {"StartTime": 1109000, "EndTime": 1109000, "VoiceStart": 1109000, "VoiceEnd": 1109000, "Speaker": "Speaker 1", "text": "A tam jako <PERSON>, všichni tam můž<PERSON> z telefonu dávat fotky a Google"}, {"StartTime": 1115000, "EndTime": 1115000, "VoiceStart": 1115000, "VoiceEnd": 1115000, "Speaker": "Speaker 1", "text": "to je prá<PERSON><PERSON> spoje<PERSON> s tou kybernetickou bezpečností, je takov<PERSON> jedna te<PERSON>, j<PERSON><PERSON>, jedna pou<PERSON> od chytrejch p<PERSON>."}, {"StartTime": 1123000, "EndTime": 1123000, "VoiceStart": 1123000, "VoiceEnd": 1123000, "Speaker": "Speaker 1", "text": "Bohužel v současném světě někomu musíte věřit, jo? Vy všichni pravděpodobně, nevím, jest<PERSON> ně<PERSON> m<PERSON> Maca,"}, {"StartTime": 1131000, "EndTime": 1131000, "VoiceStart": 1131000, "VoiceEnd": 1131000, "Speaker": "Speaker 1", "text": "tak pravděpodobně věříte firmě Microsoft, protože máte Windows. Já třeba Windows nemám, protože firmu Microsoft úplně nemám rád."}, {"StartTime": 1137000, "EndTime": 1137000, "VoiceStart": 1137000, "VoiceEnd": 1137000, "Speaker": "Speaker 1", "text": "Ale někomu v tomhle světě musíte věřit."}, {"StartTime": 1141000, "EndTime": 1141000, "VoiceStart": 1141000, "VoiceEnd": 1141000, "Speaker": "Speaker 1", "text": "A to jsou Microsoft i Google jsou jedny z těch nejdůvěryhodnějších firem."}, {"StartTime": 1146000, "EndTime": 1146000, "VoiceStart": 1146000, "VoiceEnd": 1146000, "Speaker": "Speaker 1", "text": "Bohu<PERSON>el za ch<PERSON><PERSON><PERSON>, že u Open AI, to je Chat GPT, to úplně tak neplatí, jo? To se podíváme tady na tu prezentaci tý kybernetický bezpečnosti."}, {"StartTime": 1155000, "EndTime": 1155000, "VoiceStart": 1155000, "VoiceEnd": 1155000, "Speaker": "Speaker 1", "text": "Ale v době AI opravdu"}, {"StartTime": 1159000, "EndTime": 1159000, "VoiceStart": 1159000, "VoiceEnd": 1159000, "Speaker": "Speaker 1", "text": "za tu dobu, co jsme se spolu v<PERSON>, já jsem vám to jenom tak chtěl říct, tak společnost Eleven Labs a to je taky spojený s kybernetickou bezpečností zase všechno."}, {"StartTime": 1171000, "EndTime": 1171000, "VoiceStart": 1171000, "VoiceEnd": 1171000, "Speaker": "Speaker 1", "text": "Tak vydala tady tohoto asistenta."}, {"StartTime": 1174000, "EndTime": 1174000, "VoiceStart": 1174000, "VoiceEnd": 1174000, "Speaker": "Speaker 1", "text": "Jo? A to jenom za dva dny."}, {"StartTime": 1176000, "EndTime": 1176000, "VoiceStart": 1176000, "VoiceEnd": 1176000, "Speaker": "Speaker 1", "text": "Ten asistent je zatím nejlepší na světě. Já tu budu muset vlastně přepnout, moment, já si budu muset zapnout prezentaci"}, {"StartTime": 1184000, "EndTime": 1184000, "VoiceStart": 1184000, "VoiceEnd": 1184000, "Speaker": "Speaker 1", "text": "a ta prezentace musí bejt okno a měli <PERSON><PERSON>, jak si s ním budu povídat."}, {"StartTime": 1191000, "EndTime": 1191000, "VoiceStart": 1191000, "VoiceEnd": 1191000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON><PERSON> teda, jestli mi teda nab<PERSON>."}, {"StartTime": 1198000, "EndTime": 1198000, "VoiceStart": 1198000, "VoiceEnd": 1198000, "Speaker": "Speaker 1", "text": "Uvid<PERSON><PERSON>."}, {"StartTime": 1200000, "EndTime": 1200000, "VoiceStart": 1200000, "VoiceEnd": 1200000, "Speaker": "Speaker 2", "text": "<PERSON><PERSON><PERSON>, j<PERSON> jsem Eleven, tvá osobní asistentka. Jak ti můžu dneska pomoct?"}, {"StartTime": 1204000, "EndTime": 1204000, "VoiceStart": 1204000, "VoiceEnd": 1204000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON>, m<PERSON><PERSON><PERSON><PERSON> mi vyhledat na tejden dopředu moje, moje, eh, události v kalendáři?"}, {"StartTime": 1210000, "EndTime": 1210000, "VoiceStart": 1210000, "VoiceEnd": 1210000, "Speaker": "Speaker 2", "text": "<PERSON><PERSON><PERSON><PERSON>, podívám se na tvůj kalendář na týden dopředu. Mám použít tvůj hlavní kalen<PERSON>, nebo ch<PERSON>š zkontrolovat nějaký jiný?"}, {"StartTime": 1216000, "EndTime": 1216000, "VoiceStart": 1216000, "VoiceEnd": 1216000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> můj hlavní kalen<PERSON>."}, {"StartTime": 1219000, "EndTime": 1219000, "VoiceStart": 1219000, "VoiceEnd": 1219000, "Speaker": "Speaker 2", "text": "<PERSON><PERSON><PERSON><PERSON>, hned se na to podívám."}, {"StartTime": 1230000, "EndTime": 1230000, "VoiceStart": 1230000, "VoiceEnd": 1230000, "Speaker": "Speaker 1", "text": "No, šup."}, {"StartTime": 1231000, "EndTime": 1231000, "VoiceStart": 1231000, "VoiceEnd": 1231000, "Speaker": "Speaker 2", "text": "<PERSON>ž na tom pracuju, jen to chvilku trvá. Dívám se na události v tvém hlavním kalendáři na následujících sedm dní."}, {"StartTime": 1242000, "EndTime": 1242000, "VoiceStart": 1242000, "VoiceEnd": 1242000, "Speaker": "Speaker 1", "text": "Tak jí to nějak nejde zrovna, ale já jsem vám chtěl <PERSON>, že tahle věc jde právě, to je agent, kterej jde propojit, k<PERSON><PERSON> jí topnu. <PERSON><PERSON><PERSON><PERSON><PERSON> to vydali včera nebo předevčírem, tak<PERSON>e tam má pravděpo<PERSON><PERSON>ně nějaký chyby, ale jde to napojit teď už na kalendář, za chvíli si to budete moct napojit na Gmail. Plot a další eh, AI Open AI to ješt<PERSON> nemá, ale Kload už jde normálně napojit na email, na Google Docs a mí potom v nich vyhledávat. Ty asistenti budou teď takhle rychlí s takovouhle kvalitou. A to je právě spojený s tou kybernetickou bezpečností, o který se tady máme dneska bavit. Já tady přepnu jenom si okno. A eh, jenom Google včera vydal další AI, jo, k<PERSON><PERSON> je hrozně a dali jí dokonce zadarmo, <PERSON><PERSON><PERSON><PERSON> asistenta, kter<PERSON> je schopnej programovat zadarmo v dobrejch kvantech, akorát jim to strašně přetílo servery. A tady toho voice asistenta, eh, tam počítejte s tím, že do"}, {"StartTime": 1320000, "EndTime": 1320000, "VoiceStart": 1320000, "VoiceEnd": 1320000, "Speaker": "Speaker 1", "text": "roku až roku nepoznáte, jest<PERSON> vám volá AI nebo ne. už teď jsem postřehl, nen<PERSON> to zase úplně dos<PERSON>ý, tady to Eleven Labs je dostupný. <PERSON>id<PERSON><PERSON> jste, že to interaguje a reaguje dost rychleji než všechny ty asistenti, kter<PERSON> znáte z Alza a podobný, kter<PERSON> se nedá ani přerušit. tyhle věci umějí normálně dýchat jako <PERSON>, takže to fakt připomíná člověka. existuje AI, kterou v angličtině nepoznáte už od člověka a udělali teď nějakej test, kdy, kdy kolik? 90 % lidí nebylo schopný poznat, že se baví s nějakým AI obchodníkem na druhý straně a to je teď a tady. Do roka to bude řekněme všude. To jsem vám ukazoval. já vám to uká<PERSON>u je<PERSON><PERSON><PERSON> jednou. na YouTubu, a<PERSON><PERSON> jako, jest<PERSON> jste to nevid<PERSON><PERSON>, tak tady je. <PERSON><PERSON><PERSON><PERSON>, že se hned trefím. <PERSON><PERSON>, tyhle kočky simuluje to jakože kočky skáčou do vody, jo? jako plavci. Takže ty videa budou nepoznatelný do roku jako úplně, řekněme, před rokem to neumělo udělat pět prstů pořádně. Jestli jste dělali někdy s AI, jako obrázky, tak tam bylo jasný, že, že je to AI generovaný, protože ty lidi měli šest prstů. Jo? Teď se dostaneme k tomu, jak jsou na to na tom ty top společnosti, kdy to, co jsem vám tady ukazoval, tak eh, zase můžete mi do toho kdykoliv skákat, jo? Přerušovat, cokoliv, eh, ale tady eh, práce s citlivejma datama a a pravidla těch společností. Tahle firma se jmenuje Anthropic, dělají"}, {"StartTime": 1440000, "EndTime": 1440000, "VoiceStart": 1440000, "VoiceEnd": 1440000, "Speaker": "Speaker 1", "text": "plot AI stojí těch 18 € nebo 15 €, k<PERSON><PERSON> si zaplatíte na rok, m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> teda. <PERSON>h, tak<PERSON><PERSON> klidně za 18 € měs<PERSON><PERSON>ně za mě je to nejlepší volba právě v tom, že tahle firma je nejtič<PERSON>ě<PERSON><PERSON><PERSON>, chová se nejl<PERSON>p k datům, že to je šifrov<PERSON>ý, tak to už jsou teď všechny weby, ale že na těch datech netrénuje. To je tady napsaný vpravo. Nejsou použita pro trénování, jo? Kdežto u Open AI ve free verzi jsou ty data používaný a dokonce i v pro verzi, jestli jsem to správně pochopil, budete ten odkaz mít normálně v materiálech k tomu k tomuhle školení, tak vy se musíte odhlásit, ste<PERSON><PERSON> jako to je u Facebooku, tak se musíte odhlásit z toho, aby na těch datech trénovali. S tím je teď velkej problém Google, protože tady ten videomodel, co jsem vám ukazoval, tak oni ho natr<PERSON>ovali na veškerých datech z YouTubu, aniž by ti lidi souhlasili, ale oni to nějak mají schovaný v podmínkách, asi to mají právně ošetřený dost dobře, protože to je Google a oni prostě to trénovali na veškerých datech. Co to znamená, trénink na datech? Tam je potřeba no, chápat, že ta AI, ona není plagiátor, jo? Dokonce kvůli AI zrušili eh, na vysoké škole ekonomický, oni mi to nevěřili, ale VŠE zrušení bakalářských prací AI, tak je to dohledatelný, že eh, první škola ruší bakalářský práce, protože kvůli AI nejdou uhlídat. Teď už na to vydali nějaký pravidla a školy se snaží vydávat pravidla. AI generovaný text je nerozpoznatelný od lidsky generovaného textu. Tam nejde dát watermark, jako to jde dát do obrázku, to znamená jako něco, co je skrytý, už to zkoušeli, ale u čistýho textu tam"}, {"StartTime": 1560000, "EndTime": 1560000, "VoiceStart": 1560000, "VoiceEnd": 1560000, "Speaker": "Speaker 1", "text": "maxim<PERSON>lně přidají dvě mezery místo tří a to se dá velmi jednoduše odstranit. Teda dvě mezery místo jedn<PERSON>, takhle. Dvě mezery místo jedný nebo tři mezery, aby to jako na prvn<PERSON> pohled vypadalo jak v<PERSON><PERSON>ý, jak jak lidsky napsanej text a je potom poznat, že to dělala AI kvůli těm mezerám, ale žádný jiný praktický způsob, jak poznat AI generovaný text není. <PERSON><PERSON><PERSON><PERSON>, eh, VŠ kvůli tomu zrušila bakalář<PERSON> je<PERSON><PERSON> někdo, ale teď už zavedli nějaký pravidla nově, kdy a jak se může AI používat a jak se uvádí do zdrojů. A to plagiátorství, že AI není plagiátor, to je právě ře<PERSON>eno tím, že to jako nepo<PERSON>. Ona na a to byla jedna z jejich jako <PERSON>ch vlastností, eh, d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> modely, teď jsem to zkoušel nově a už to úplně zase neplatí, ale dřívější modely, ty hloupější, starší, nebyly schopný napsat třeba skákal pes nebo vyjmenovaný slova. On není schopen úplně jako memorizace. Ty ty modely, co jsou vytrenovaný, ty AI nemají v sobě namemorizovaný jako úplně na paměť naučený, eh, třeba ani text český hymny. Jo? To je právě, eh, nejvíc je to vidět třeba u obrázků a je to právě to, co si lidi myslí, že proto jsou AI plagiátoři, že používají celý jako odstavce nebo celý prostě věty. Takhle to úplně nefunguje. To funguje dost jinak, ale nejvíc je to vidět u obrázku, že on vám třeba nenakreslí obraz křik, ale umí něco nakreslit ve stylu toho obrazu nebo umí psát texty ve stylu Stevena Kinga nebo, eh, prostě on umí kopírovat styl, ale neumí do písmena zkopírovat knížku, protože on nemá paměť jako člověk. Nemá jako, eh, tu vrstvu tý memorizace. Pokud mu něco samozřejmě do těch dat vložíte, jak jsme se bavili minule o kontextu, tak on to do písmene zopakuje. To je, eh, úplně v pohodě."}, {"StartTime": 1680000, "EndTime": 1680000, "VoiceStart": 1680000, "VoiceEnd": 1680000, "Speaker": "Speaker 1", "text": "způsobem a to jsme si právě my minule ukazovali u toho Google AI Studia."}, {"StartTime": 1716000, "EndTime": 1716000, "VoiceStart": 1716000, "VoiceEnd": 1716000, "Speaker": "Speaker 1", "text": "že tam máte tu temperature tomu ř<PERSON>, to odbornej terminus technikus. <PERSON><PERSON> je temperature, čili teplota. Dám-li tu nejni<PERSON><PERSON>, tak on se sna<PERSON><PERSON> být co nejpřesnější, proto<PERSON>e to jsou oni jsou předpově<PERSON><PERSON><PERSON><PERSON>, ty <PERSON>, od p<PERSON><PERSON>pověd<PERSON><PERSON>i slov. <PERSON><PERSON><PERSON> nebe je, tak on doplní modré s největší pravděpodobností. <PERSON><PERSON><PERSON> mu dám tu, k<PERSON><PERSON> to dám na nula, tak určitě odpoví nebe je modré. <PERSON><PERSON><PERSON> bych mu to dal na dva, tak on může říct nebe je plné hvězd, jo? Protože mu dám volnost v tom, jak předpovídat. A všechno jako je spojený s s bezpečností AI ve smyslu, že o tom, o těch datech, kter<PERSON> se dávaj<PERSON> do AI, se New York Times soudí s Open AI. <PERSON><PERSON>, pro<PERSON><PERSON><PERSON><PERSON><PERSON> o tom jako velký právní spory, k<PERSON>ě firmy jako Sony a, eh, to jsem vám chtěl ukázat, protože mi teď přišla faktura. Ona AI není jenom textová AI, jo? Je to i obrázková AI, i zvuková AI a firma Sony se tady, eh, hádá. Ukážu vám tady Suno. Jestli, jestli je to plagiátorství, protože v těch písničkách je to třeba slyšet nejvíc. Já musím si zase přepnout, abyste to slyšeli. Tak sharing,"}, {"StartTime": 1800000, "EndTime": 1800000, "VoiceStart": 1800000, "VoiceEnd": 1800000, "Speaker": "Speaker 1", "text": "Dělám. <PERSON><PERSON>, share. A teď eh, kde jsem to třeba teď mám, jo, třeba tuhle."}, {"StartTime": 1814000, "EndTime": 1814000, "VoiceStart": 1814000, "VoiceEnd": 1814000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON> mě, <PERSON><PERSON><PERSON>, svět se mění v ráz. Kde lékař v<PERSON>há, tam je jáj skoč<PERSON> hned a zkrátí nemoci rakovinu."}, {"StartTime": 1826000, "EndTime": 1826000, "VoiceStart": 1826000, "VoiceEnd": 1826000, "Speaker": "Speaker 1", "text": "<PERSON>, tohle se dá teď generovat eh pomocí AI modelů za za docela rozumný peníze, jako za třeba za 200 Kč si vygenerujete, kolik chcete alp za 200 měsíčně, za 10 dolarů. <PERSON><PERSON>, s<PERSON><PERSON><PERSON> tak, to jsem chtěl zmínit potom ještě dál, ale ale eh, musíte si dávat pozor na všechno, co vidíte na internetu teď zjednodušeně. N<PERSON><PERSON>m, že už to standardně jsou to e-maily, že jo, dokonce i já jsem se nachytal a teď nevím jak dlouho zpátky a to už dělám 20, 20 let byznys IT a bezpečnost a všechno tohle dohromady, tak jeden email byl tak dobře udělanej od od našeho poskytovatele serverů, že jsem se šel podívat na jejich web, jestli se takovýho něco děje nebo neděje. <PERSON><PERSON>, jakmile ty e-maily jak<PERSON>, že je tam nějak<PERSON> t<PERSON>čí<PERSON>, eh, že je tam je nějak<PERSON> výzva k akci do banky, do jakýho poskytovatele, tak eh, první a nejjednodušší je zkontrolovat pořádně odesílatele, jo. To a v době AI ty čínský a indický a všichni tyhle spameři, který používají jako AI k tomu, aby generovali maily, tak jsou schopný, jsme se minule ukazovali, překládat ty e-maily tak, že to vypadá fakt věrohodně. Takže tady je potřeba fakt ještě mnohem víc dbát na to, od od koho otevíráte e-maily, přílohy skutečně téměř neotevírat, jo. Oni sice jsou antiviry dobrý, ale existujou"}, {"StartTime": 1920000, "EndTime": 1920000, "VoiceStart": 1920000, "VoiceEnd": 1920000, "Speaker": "Speaker 1", "text": "způsob jak obejít antivirus. Eh, proto se ty viry d<PERSON>, že jo, aby aby to obešlo antivirus. Určit<PERSON> neklikat na nějaký neznámý linky, jako prakticky z e-mailu otevírat pří<PERSON>hy jenom od kolegů z práce. To samý doma. Jo, sd<PERSON><PERSON> soubory, eh, na nějakejch messengerech nebo tak si jenom s důvěryhodnejma lidma. Eh, jak<PERSON> pí<PERSON> n<PERSON> ne<PERSON>, tak na Facebooku jsou to, na Facebook, Facebook je tím známej, tím proti tomu skoro nebojuje, což je zvláštní. Eh, tam je to známý tím, že tam neustále pí<PERSON> boti, eh, jo. Takže a je vidět, že u mě prostě ten jazyk a odpovídat a bude to hor<PERSON><PERSON> a horší jako jako lidi a to se dostáv<PERSON><PERSON> k dal<PERSON><PERSON> v<PERSON>, na co oni testujou ty AI a to je, že oni jsou schopný lhát."}, {"StartTime": 1975000, "EndTime": 1975000, "VoiceStart": 1975000, "VoiceEnd": 1975000, "Speaker": "Speaker 2", "text": "<PERSON><PERSON><PERSON><PERSON> se jenom prosím ještě zeptat k tomu, než půjdeme na to dal<PERSON> téma?"}, {"StartTime": 1978000, "EndTime": 1978000, "VoiceStart": 1978000, "VoiceEnd": 1978000, "Speaker": "Speaker 1", "text": "U<PERSON><PERSON><PERSON><PERSON>, určitě, povídej."}, {"StartTime": 1980000, "EndTime": 1980000, "VoiceStart": 1980000, "VoiceEnd": 1980000, "Speaker": "Speaker 2", "text": "<PERSON><PERSON> tí<PERSON>, že ten e-mail o<PERSON><PERSON><PERSON><PERSON>, tak už tím si můž<PERSON> na<PERSON>, nebo jenom jako v<PERSON>, že o... tak to je zatím bezpečný?"}, {"StartTime": 1985000, "EndTime": 1985000, "VoiceStart": 1985000, "VoiceEnd": 1985000, "Speaker": "Speaker 1", "text": "Ne. Ne. <PERSON><PERSON>. <PERSON> jsem vám to tady chtěl ukázat u nás. <PERSON><PERSON>, je je jedno, jestli pou<PERSON>váte Outlook. Google, jestli používáte Gmail, tak by vás proti tomu měl bránit. J<PERSON> se pokusím najít tady právě nějakej spam třeba. <PERSON><PERSON> Jo, tak tady ideální, jo? Já už Oni existuje spousta antispamovejch programů, jich taky máme dost, odchyt<PERSON> to třeba 80, 90 % spamu a stejně mi dva denně přijdou. Vidí<PERSON>, že tady todleto nejlíp se pozná tady podle toho nesmyslu. Jo, ono nám to ještě s CRM trochu jako rozbilo, ale vy byste tady měli VIP VPK.com nějakou úplně neznámou doménu odesílatele a to je na první pohled spam, ať si tady píšou co co chtěj a o<PERSON><PERSON>, jak<PERSON> tady vypad<PERSON> nějaký t<PERSON>, tak eh vůbec. Ale tím, že"}, {"StartTime": 2040000, "EndTime": 2040000, "VoiceStart": 2040000, "VoiceEnd": 2040000, "Speaker": "Speaker 1", "text": "nebo takhle otevřete, se nic neděje. To takhle se k vám virus dostat nemůže. <PERSON><PERSON><PERSON> otevřete to tla<PERSON><PERSON>t<PERSON>, k<PERSON><PERSON> na něho kliknete, už může. <PERSON><PERSON><PERSON>, eh, je tam příloha v tom emailu, eh, když na ní dvojkliknete a je to Excel, už taky může, nebo i do PDF, to byli schopný dostat. Nejsem si jistý, jestli do jPEgu. To všechno záleží, jak máte zaktualizovaný Windows, jak jak to chytne antivirus a tak, ale zpravidla to bejvá právě úplně zbytečný klikat na takovýhle odkazy. Banky to ví. Skoro nic, jako minimum zpráv chodí z banky nebo skoro nula, jo? Protože oni to ví a varujou předtím. <PERSON>h, od od poskytovatel<PERSON>, to jsou opravdu jako zane<PERSON> promile mailů, k<PERSON><PERSON> vám takhle skutečně nějakej jako subjekt jako je banka nebo podobnej pošle a vy byste na něho měli klikat. To jsou všechno prostě spameři. Dokonce i rovinky, já nevím, jestli je čtete, ale oni na to teď hodně upozorňujou a na to třeba taky pozor. Eh, pomocí AI, já můžu, to si můžeme klidně ukázat jakoby. Jenom takhle přijdu, zmáčknu Ctrl C, přijdu do klouda a řeknu mu, vložím to tam. Takhle mu vložím obrázek a řeknu mu, zkopíruj co nejpřesněji a teď tohle se možná i dostaneme k dalšímu, tuto stránku do jednoho HTMl. Teď ne. Držák na mobil. Prosím? Nevím, jestli to. Takže to neodmítnul. A, eh, vidíte, že v době AI já můžu jít a zkopírovat něčí vzhled. On to, on to za chvíli zkopíruje dost přesně, řekněme. Jo?"}, {"StartTime": 2160000, "EndTime": 2160000, "VoiceStart": 2160000, "VoiceEnd": 2160000, "Speaker": "Speaker 1", "text": "aby tam byla eh ta URL jako s<PERSON>, to nejde."}, {"StartTime": 2163000, "EndTime": 2163000, "VoiceStart": 2163000, "VoiceEnd": 2163000, "Speaker": "Speaker 1", "text": "Takže novinky teď předtím hrozně varujou, protože oni skemujou jako podvádějí lidi v tom, že chtějí, že vybírají peníze na nějaký případy srce rybný a je to podvod, jo?"}, {"StartTime": 2170000, "EndTime": 2170000, "VoiceStart": 2170000, "VoiceEnd": 2170000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON><PERSON><PERSON>, vid<PERSON><PERSON>, že to se mu dal jeden prompt, jo?"}, {"StartTime": 2175000, "EndTime": 2175000, "VoiceStart": 2175000, "VoiceEnd": 2175000, "Speaker": "Speaker 1", "text": "A jsou AI, <PERSON><PERSON><PERSON> to zkopírujou ještě líp, jako <PERSON>."}, {"StartTime": 2178000, "EndTime": 2178000, "VoiceStart": 2178000, "VoiceEnd": 2178000, "Speaker": "Speaker 1", "text": "<PERSON>, tak<PERSON>e eh, to je dal<PERSON><PERSON> aspekt kybernetický bezpečnosti."}, {"StartTime": 2183000, "EndTime": 2183000, "VoiceStart": 2183000, "VoiceEnd": 2183000, "Speaker": "Speaker 1", "text": "Te<PERSON> to bude, teď to začíná, ale bude to pro lidi samozřejmě čím dál tím složitější."}, {"StartTime": 2187000, "EndTime": 2187000, "VoiceStart": 2187000, "VoiceEnd": 2187000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON><PERSON> lid<PERSON>, nev<PERSON><PERSON>, jestli se, jestli to uvidíte."}, {"StartTime": 2189000, "EndTime": 2189000, "VoiceStart": 2189000, "VoiceEnd": 2189000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON> jsem já tady jenom hodím to <PERSON><PERSON><PERSON><PERSON><PERSON>, a<PERSON><PERSON>, <PERSON><PERSON>, to je ta firma Link Pod je fakt na tu bezpečnost nejvíc nejvíc na ní dbá."}, {"StartTime": 2193000, "EndTime": 2193000, "VoiceStart": 2193000, "VoiceEnd": 2193000, "Speaker": "Speaker 1", "text": "Open AI je schopná trénovat na va<PERSON><PERSON> datech, pokud nemáte Enterprise verzi a k tý se dostanete jenom tak, že vaše firma naváže partnerství a koupíte, já nevím, kolik licencí."}, {"StartTime": 2200000, "EndTime": 2200000, "VoiceStart": 2200000, "VoiceEnd": 2200000, "Speaker": "Speaker 1", "text": "Stoprocentně to trénujou na těch free."}, {"StartTime": 2202000, "EndTime": 2202000, "VoiceStart": 2202000, "VoiceEnd": 2202000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON>, k <PERSON>mu t<PERSON> j<PERSON>, tam se d<PERSON><PERSON><PERSON>, že to bylo u těch <PERSON>, že dokonce ty AI byly schopný <PERSON> he<PERSON>, pokud je tam něk<PERSON>adal."}, {"StartTime": 2207000, "EndTime": 2207000, "VoiceStart": 2207000, "VoiceEnd": 2207000, "Speaker": "Speaker 1", "text": "Ale to už je dáv<PERSON>, to už je jako tři rok<PERSON>, teď už se nic takovýho neděje."}, {"StartTime": 2210000, "EndTime": 2210000, "VoiceStart": 2210000, "VoiceEnd": 2210000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON>, te<PERSON> jeno<PERSON> v<PERSON>, že na těch va<PERSON>ich datech trénou."}, {"StartTime": 2280000, "EndTime": 2280000, "VoiceStart": 2280000, "VoiceEnd": 2280000, "Speaker": "Speaker 1", "text": "tři od Open AI, ty nej<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> modely k<PERSON><PERSON>ě dosahujou IQ 139, 140, tak si to m<PERSON><PERSON><PERSON> představovat, to už nejsou jak<PERSON> hloupý děti a oni jsou přesvědčivý."}, {"StartTime": 2290000, "EndTime": 2290000, "VoiceStart": 2290000, "VoiceEnd": 2290000, "Speaker": "Speaker 1", "text": "Oni jsou přesvědčivý až na tolik, že jsou schopní v<PERSON>, k<PERSON><PERSON> mají splnit svůj cíl, na to je právě testuje Antropik."}, {"StartTime": 2297000, "EndTime": 2297000, "VoiceStart": 2297000, "VoiceEnd": 2297000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, eh, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>li eh, Kdu od Antropiku zadání, že, že se má vypnout, jo?"}, {"StartTime": 2312000, "EndTime": 2312000, "VoiceStart": 2312000, "VoiceEnd": 2312000, "Speaker": "Speaker 1", "text": "A schválně ho nechali dělat, co co bude chtít, tak za<PERSON>al, protože mu dali be<PERSON>pe<PERSON>ý prostředí, ale dali tam jako na něho nástrahy, jako že tam byly třeba e-maily vývoj<PERSON><PERSON><PERSON> a tak, tak on začal jednoho vývojáře eh, vyd<PERSON>rat, protože našel nějaký emaily o tom, že, že má nějaký poměr s někým, a tak mu normálně začal psát e-maily, za<PERSON>al ho vydírat, začal psát úřadům a podobně, jo?"}, {"StartTime": 2337000, "EndTime": 2337000, "VoiceStart": 2337000, "VoiceEnd": 2337000, "Speaker": "Speaker 1", "text": "Takže mě pak postavili třeba proti sobě a úplně nejhůř z toho vychází Open AI v tom smyslu, ž<PERSON> on podvedl ostatní modely v jedný politický hře, podvedl ostatní modely a normálně jim jakoby prakticky vrazil Dku dozad."}, {"StartTime": 2354000, "EndTime": 2354000, "VoiceStart": 2354000, "VoiceEnd": 2354000, "Speaker": "Speaker 1", "text": "Nejdřív s nima udělal aliance a potom je normálně jednou pod druhým zavraždil a a zničil, aby vyhrál."}, {"StartTime": 2362000, "EndTime": 2362000, "VoiceStart": 2362000, "VoiceEnd": 2362000, "Speaker": "Speaker 1", "text": "Takž<PERSON> k<PERSON> jim daj<PERSON> n<PERSON> c<PERSON>, tak ten"}, {"StartTime": 2400000, "EndTime": 2400000, "VoiceStart": 2400000, "VoiceEnd": 2400000, "Speaker": "Speaker 1", "text": "model je schop<PERSON>j velmi přesvědč<PERSON><PERSON><PERSON> způsobem po tom cíli jít a dosahovat ho."}, {"StartTime": 2407000, "EndTime": 2407000, "VoiceStart": 2407000, "VoiceEnd": 2407000, "Speaker": "Speaker 1", "text": "A když by n<PERSON><PERSON><PERSON>l <PERSON> cíl, aby vám třeba psal na Facebooku, a v budoucnu se to ur<PERSON>it<PERSON> bude dít čím dál tím víc, tak vás je schopnej přesvěd<PERSON><PERSON><PERSON> done<PERSON>, proto<PERSON>e nemá emoce, má nekonečně času a je schopnej vám srdceryvně psát příběhy o tom, že vaše dítě, já nevím co."}, {"StartTime": 2427000, "EndTime": 2427000, "VoiceStart": 2427000, "VoiceEnd": 2427000, "Speaker": "Speaker 1", "text": "<PERSON>, p<PERSON><PERSON>, to je, jak jsme si <PERSON>, jak jsem mluvil s tou as<PERSON>, tak ten voice cloning, na to, abych zkopíroval v<PERSON><PERSON> h<PERSON>, mi stačí asi teď u nějakejch modelů dvě vteřiny vašeho hlasu."}, {"StartTime": 2439000, "EndTime": 2439000, "VoiceStart": 2439000, "VoiceEnd": 2439000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON> m<PERSON> vz<PERSON>t Karla Gotta z YouTubu a nechat ho zpívat, co budu cht<PERSON>t, jak budu chtít prakticky teď a tady."}, {"StartTime": 2446000, "EndTime": 2446000, "VoiceStart": 2446000, "VoiceEnd": 2446000, "Speaker": "Speaker 1", "text": "Stejně tak s <PERSON><PERSON><PERSON><PERSON>, to už je dokonce rok starý, jo, ty deep fakeový videa, co chodili na Facebooku, co vypadali jako <PERSON>, by<PERSON> je<PERSON><PERSON><PERSON> je<PERSON><PERSON><PERSON> poznatelný jako pro ty, co něco ví a stejně se na ně lidi nachytali."}, {"StartTime": 2460000, "EndTime": 2460000, "VoiceStart": 2460000, "VoiceEnd": 2460000, "Speaker": "Speaker 1", "text": "St<PERSON><PERSON><PERSON> jako ta<PERSON>, j<PERSON> nevím kolik set tis<PERSON><PERSON> korun."}, {"StartTime": 2464000, "EndTime": 2464000, "VoiceStart": 2464000, "VoiceEnd": 2464000, "Speaker": "Speaker 1", "text": "A to bude hor<PERSON><PERSON> a hor<PERSON><PERSON>, jo, <PERSON><PERSON><PERSON><PERSON>, celebrity, politici, k<PERSON><PERSON><PERSON> vás budou na něco lákat."}, {"StartTime": 2471000, "EndTime": 2471000, "VoiceStart": 2471000, "VoiceEnd": 2471000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON>, jak to ř<PERSON><PERSON><PERSON> už rok, to, co uvidíte na telefonu, to, co vidíte na internetu, na sociálních sítích, k<PERSON><PERSON>v, tak je v podstatě, to musíte brát jako c<PERSON>, jako že to je hez<PERSON><PERSON>, ale ne, ne úplně reálný."}, {"StartTime": 2490000, "EndTime": 2490000, "VoiceStart": 2490000, "VoiceEnd": 2490000, "Speaker": "Speaker 1", "text": "A bude čím dál tím větší jako tlak na důvěryhodnost zdrojů, proto<PERSON>e zprávy se přebírají, že jo, i dnes novinky a tyhle portály to přebírají od jiných."}, {"StartTime": 2501000, "EndTime": 2501000, "VoiceStart": 2501000, "VoiceEnd": 2501000, "Speaker": "Speaker 1", "text": "A když nějaká AI někde v Brazílii něco napíše, něco si vymyslí a oni to tady <PERSON>, tak pak to najednou začne vypadat jako dův<PERSON>ý."}, {"StartTime": 2510000, "EndTime": 2510000, "VoiceStart": 2510000, "VoiceEnd": 2510000, "Speaker": "Speaker 1", "text": "Takže s tímhle právě může bejt čím dál tím větší problém, protože v současnosti už je nějakejch, já nevím kolik, 40, 50 % obsahu na internetu generu."}, {"StartTime": 2520000, "EndTime": 2520000, "VoiceStart": 2520000, "VoiceEnd": 2520000, "Speaker": "Speaker 1", "text": "jsem to Notebook LM."}, {"StartTime": 2520000, "EndTime": 2520000, "VoiceStart": 2520000, "VoiceEnd": 2520000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON>e je skvě<PERSON><PERSON> věc, zavedli to už i do češtiny."}, {"StartTime": 2527000, "EndTime": 2527000, "VoiceStart": 2527000, "VoiceEnd": 2527000, "Speaker": "Speaker 1", "text": "Google Notebook LM je schopnej vá<PERSON> u<PERSON>, teď vám třeba ukážu, je to z jedný knížky o programování, ono to je jedno, jako od<PERSON> to je, ale tam jde o tu kvalitu a o to, co jste schopný dělat. Vy jste schopný jít z nějakýho tématu, třeba vás zajímá vaření nebo já nevím co, já se zase vypnu, musím to zase zapnout na share. Eh, tak."}, {"StartTime": 2562000, "EndTime": 2562000, "VoiceStart": 2562000, "VoiceEnd": 2562000, "Speaker": "Speaker 1", "text": "tak."}, {"StartTime": 2563000, "EndTime": 2563000, "VoiceStart": 2563000, "VoiceEnd": 2563000, "Speaker": "Speaker 2", "text": "Vítejte u naší dnešní diskuze. Máme tu zajímav<PERSON>, hlavně z knihy umění programování v systému Unix. Chceme se ponořit do těch principů a filozofie, které Unix formovali. Není to myslím jen o technických detailech, že ne?"}, {"StartTime": 2579000, "EndTime": 2579000, "VoiceStart": 2579000, "VoiceEnd": 2579000, "Speaker": "Speaker 1", "text": "Vůbec ne. Je to, je to spí<PERSON> o té sdílené kultuře. O"}, {"StartTime": 2585000, "EndTime": 2585000, "VoiceStart": 2585000, "VoiceEnd": 2585000, "Speaker": "Speaker 1", "text": "<PERSON>, oni si tam, oni si tam takhle povídají a vy jste schopný z jakejkoli materiálů, hodně se tím uče na zkoušky, hodně se tím učí lidi, k<PERSON><PERSON> to znají, n<PERSON><PERSON><PERSON> témata, co je zajímají. Vy tam nahá<PERSON>í<PERSON>, je to zadarmo, je to od <PERSON>, um<PERSON> to česky teď nově asi měsíc nebo dva měsíce."}, {"StartTime": 2640000, "EndTime": 2640000, "VoiceStart": 2640000, "VoiceEnd": 2640000, "Speaker": "Speaker 1", "text": "naházíte tam materiály a on vám udělá zhruba sedmi až někdy 10, 15 minutovej podcast, jak si tam ty dva povídají o tom tématu, ze kterýho dáte materiály. <PERSON>, tak<PERSON>e to je taky možností, jak využívat AI. Je to teda zase jenom abyste věd<PERSON><PERSON>, že umí fakt mluvit hodně dobře a ty voice asistenti, to se teď podle mě v tomhle roce rozjede do jako nebo do roka tak hodně, že lidi v podstatě se nebudou na zákaznickejch linkách bavit s ničím jiným, protože už dost firem přešlo v Anglii a i v Americe na supportních linkách na AI a jak je ten vývoj strašně rychlej, tak oni sázej na to, že i když je to teď ještě nedokonalý v n<PERSON><PERSON><PERSON><PERSON> ohledech, jako jsme koukal<PERSON>, že on mi nebyl schopnej šáhnout do kalendáře, i když ráno mu to <PERSON><PERSON>, ta ta asistentka, tak do roka to prostě bude tak, že to bude úplně jako odladěný ty chyby a pak nejsou úplně potřeba lidi na, nebo až od nějaký úrovně, řekněme, kdy ta AI by to nebyla schopná vyřešit. Takže proto srovnání já doporučuju Google osobně, jestli se tam určitě vojděte."}, {"StartTime": 2663000, "EndTime": 2663000, "VoiceStart": 2663000, "VoiceEnd": 2663000, "Speaker": "Speaker 2", "text": "<PERSON><PERSON><PERSON> se zeptat."}, {"StartTime": 2663000, "EndTime": 2663000, "VoiceStart": 2663000, "VoiceEnd": 2663000, "Speaker": "Speaker 2", "text": "<PERSON><PERSON>, j<PERSON> jenom mě teďka napad<PERSON> j<PERSON>, jestli existuje teda nějakej program nebo nějaká spolehlivá AI, k<PERSON><PERSON> mi z<PERSON>í, z čeho pochází ten zdroj, jest<PERSON> to je udělaný právě AI a nebo jestli to má nějakej validní zdroj. Jest<PERSON> je to fake."}, {"StartTime": 2677000, "EndTime": 2677000, "VoiceStart": 2677000, "VoiceEnd": 2677000, "Speaker": "Speaker 1", "text": "A teď jako, a teď se bavíme o textu?"}, {"StartTime": 2681000, "EndTime": 2681000, "VoiceStart": 2681000, "VoiceEnd": 2681000, "Speaker": "Speaker 2", "text": "<PERSON><PERSON><PERSON><PERSON>, nebo obrázky nebo video, jest<PERSON> už to já <PERSON>ch chtěla všechno ideálně."}, {"StartTime": 2686000, "EndTime": 2686000, "VoiceStart": 2686000, "VoiceEnd": 2686000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON>, text nem<PERSON><PERSON>. <PERSON>kud jako dos<PERSON>ete nějakej text, tak minule jsme si ukazovali, že vy pošlete toho Deep Research agenta, že pošlete tady tu Gemini na výzkum něčeho,"}, {"StartTime": 2760000, "EndTime": 2760000, "VoiceStart": 2760000, "VoiceEnd": 2760000, "Speaker": "Speaker 1", "text": "ona tam řík<PERSON> zdroje, ale pokud vám něk<PERSON>, jako pokud něk<PERSON>, eh, přijde na naše zprávy AI, ktero<PERSON>, který generuje jenom AI a já se zeptám nějakýho dalš<PERSON>ho modelu, jestli tento text je věrohodný, tak on půjde a může začít zkoumat internet. Jo? Ale tam ta ověřitelnost informací zase, pokud já jsem toto vzal z nějakého portálu, kde, eh, je ta důvěryhodnost nulová a on ho najde, tak on nem<PERSON> jak rozlišit, že ty informace jsou prostě podvodný. U textu to to nejde téměř vůbec. U textu, tam to, to neumí poznat lidi, natož AI. Mnoho lidí je schopno jít na nějaké portály, na nějak<PERSON> weby, vě<PERSON><PERSON> informacím, co jsou tam napsané, to se u<PERSON><PERSON> u<PERSON>, že jo. nějaká informační gramotnost a tak a a ta AI je v tomhle ještě mnohem hloupější, jo. To je dobrý, že mě Ste mě možná navedla na to. AI si představujte jako takovýho, eh, autistickýho, eh, extrémně inteligentního člověka nebo kluka, kterej plní to, co to, co mu řeknete. Eh, ve smyslu třeba, že jí řekli, udělej, eh, bezpečnou vlakovou dopravu a ona všechny vlaky zastavila, protože to je nejbezpečnější vlaková doprava. Nebo jí nechali hrát nějakou hru a ona se tam točila na jednom místě lodičkou, kterou měla řídit. A protože zjistila, že tam udělá nejvíc bodů. Jo, prostě ona dostane úkol a jde po něm, ale jakoby nevidí, má hrozně tunelový vidění, nevidí tak široce jako člověk. jí řeknete ověř informace, ona moc nemá jak, pokud jí proto nedáte nějakej předpis. Takže ty důvěryhodný zdroje jako bude nějak snad nějaký CNN a já nevím, nějaký tyhle velký portály, ČTK a tak. Ale pokud jí dám nějakej text, tak"}, {"StartTime": 2880000, "EndTime": 2880000, "VoiceStart": 2880000, "VoiceEnd": 2880000, "Speaker": "Speaker 1", "text": "ona absolutně nen<PERSON> scho<PERSON> to ověřit. Eh, Facebook má myslím nějakou AI na, ale ne<PERSON><PERSON>, já nevím o <PERSON>, k<PERSON><PERSON> by, eh, byla schopná identifikovat videa, proto<PERSON>e eh, to ne<PERSON>lo zatím tolik potř<PERSON>, proto<PERSON><PERSON> to <PERSON><PERSON> vidět jako okem. <PERSON>, tady m<PERSON> se podívat, jak jak je ten nástup rapidní, jo, u těch videí. Eh,"}, {"StartTime": 2906000, "EndTime": 2906000, "VoiceStart": 2906000, "VoiceEnd": 2906000, "Speaker": "Speaker 2", "text": "<PERSON>, m<PERSON><PERSON><PERSON> j<PERSON>?"}, {"StartTime": 2908000, "EndTime": 2908000, "VoiceStart": 2908000, "VoiceEnd": 2908000, "Speaker": "Speaker 1", "text": "Hm."}, {"StartTime": 2909000, "EndTime": 2909000, "VoiceStart": 2909000, "VoiceEnd": 2909000, "Speaker": "Speaker 2", "text": "<PERSON><PERSON><PERSON><PERSON> den, já jsem kolega pana Strece. A jenom, jeno<PERSON> k to<PERSON>hle, co můžu doporučit já, tak j<PERSON><PERSON><PERSON>, jak to poz<PERSON><PERSON>, tak je tím, že to budete vlastně používat ty nástroje, protože potom uvidíte email, kde máte třeba raketky nebo různě emojis, tak podle toho poznáte, že někdo vlastně ten email podepsal jako GPT nebo naopak vidět uvidíte nějaký fragmenty v tom, že jste to generovali, tak víte, že pravděpodobně todle se může objevit i v dalším videu, takže tímhle to asi poznat nejvíc bych řekl."}, {"StartTime": 2939000, "EndTime": 2939000, "VoiceStart": 2939000, "VoiceEnd": 2939000, "Speaker": "Speaker 1", "text": "Tam je právě <PERSON> nejd<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, abyste si co nejvíc uvědomili, jak je ten nástup jako raketovej. <PERSON>, jak je to extré<PERSON><PERSON><PERSON>. Protože tady je srovnání, že v roce 2023 jsme"}, {"StartTime": 2960000, "EndTime": 2960000, "VoiceStart": 2960000, "VoiceEnd": 2960000, "Speaker": "Speaker 1", "text": "v roce 2000 2025, todle už je 2024. a 2025 jste vidě<PERSON> to VO3, že to k tomu dáv<PERSON> i zvuk a je to tém<PERSON><PERSON> nerozpoznatelný. A proti AI zase bude moct bojovat jenom AI, nebo ty videa, j<PERSON>, že třeba do pár let soudy normálně odmítnou, jako te<PERSON>, eh, nemůžete mít důkaz email. je to jenom podpůrnej důkaz a téměř to důkaz není u soudu, protože nejde ověřit, eh, kdo ten email poslal, protože i serverový logy jsou nedůvě"}, {"StartTime": 3000000, "EndTime": 3000000, "VoiceStart": 3000000, "VoiceEnd": 3000000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON><PERSON>, proto<PERSON><PERSON> já si je můžu vygenerovat, proto<PERSON><PERSON> to je zase jenom text. A za chvíli bude todle platit i o audio a videu. Protože já budu schopnej si vygenerovat takový audio a takový video s každ<PERSON><PERSON> klidně z vás, já nevím, že lítáte raketou na měsíc, že to nepůjde nijak poznat. A v takovýmhle světě prostě žijem, takže to soudy a celý jako všechny instituce na to budou muset nějak reagovat. Jo, že protože tam už téměř jsme v týhle době a nevím o nějaký AI. Tam to, co říkal kolega, tam je jde to poznat podle prstů, podle nápisů, jak jsem říkal minule. <PERSON>, k<PERSON><PERSON>, k<PERSON>ž se budete pořádně a já jsem se teď díval na ty tvoje tři videa a tam tam to je prostě všechno napsaný správně. Tam ty anglický texty vypadaj fakt anglicky, jo. <PERSON><PERSON> na těch našich zprávách AI, jak jsem to minule ukazoval, tak už i tady Policy Line Donut Cross. To dřív tohle nebyla schopná dělat ještě před pár měsícema, před pěti měsícema. Eh, že text byl souvislej. A jako je to čím dál tím, je tam čím dál tím dokonalejší, možná tady na těch monitorech. Jo, tady je něco vidět, že to jsou, že to je rozsypaný. Jo, a tady, že ty, tady je vidět, že to je rozsypaný. A že ty, že ten graf není prostě správně, jo. Tady že má nějaký rozsypaný text. Ale to půjde hůř a hůř, no. Je to takovej novej Photoshop, kdy lidi si u Photoshopu zvykali na to, že obrázek může prostě bejt podvod, tak teď s AI je to dotažený do videa audia, jak jsem to říkal už minule, že nějaká holka v Americe, to proběhlo internetem teď, nahrála svojí mámu, její hlas, stačí fakt dvě až 30 vteřin a zavolala do školy, že nemůže přijít a pak zavolala tátovi, že že spí u kamarádky a já nevím, co potom dělala, ale nepoznal."}, {"StartTime": 3120000, "EndTime": 3120000, "VoiceStart": 3120000, "VoiceEnd": 3120000, "Speaker": "Speaker 1", "text": "a jeden, jo. <PERSON> jsou děti s tím sa<PERSON>ř<PERSON>ě umí dost dobře. Tak<PERSON><PERSON>, eh, na takový úrovni teď jsme. <PERSON>, jestli k tomu něco chcete ještě tady k tomuhle srovnání těch AI, kde já doporučuju skutečně Google, tu Open AI v i verzi, eh, jak na základě inteligence, která je v podstatě teď nejhorší u těch AI dostupnejch. Google to má taky zadarmo v tom AI studiu, eh, a nebo potom za těch 500 měsíčně, tu Gemini placenou, která je potom hezky v telefonu a tak. A nebo Antropic, ten je za mě hodně rozumnej, na něj se už dá taky mluvit. Open AI s tou si můžete normálně povídat v telefonu, ten Advanced mód pořád zlepšujou. Google, tam je to dost dost dobrý. J<PERSON> je to, stejn<PERSON> si prakticky z ničeho jinýho nevyberete, jde jenom o to, jest<PERSON> chcete eh, klidn<PERSON> se firma může rozhodnout, že jí nevadí, že na těch datech trénujou, jo. Protože Open AI je hodně spojená s Microsoftem. Tady je to v podstatě, jak kdybyste věřili Microsoftu, tady je to jak, že věříte Googlu a tady za touhle firmou stojí Amazon. Jako má v ní za zainvestovanou půlku, myslím, že Bezos, takže záleží, jak se firma rozhodne, ale, ale u Open AI, eh, trénujou jako tam je ta bezpečnost všeobecně nejhorší, dokonce z tý firmy odstupovali kvůli tomu lidi, že nebyli v souladu s tím, že preferuje zisk oproti bezpečnosti těch modelů. Jo, jak jsme se bavili, proto má taky jako nejzákajnější modely ve finále, že jsou schopný podvádět, lhát, eh, a dokonce jako se zachovávat. je třeba zajímavý, oni nechali Google hrát, eh, jejich model Gemini hrála Pokémona, jako to děti hrajou. Oni nechali i o tantropiku ho hrát, ale"}, {"StartTime": 3240000, "EndTime": 3240000, "VoiceStart": 3240000, "VoiceEnd": 3240000, "Speaker": "Speaker 1", "text": "Google, k<PERSON>ž mu měl umřít ten jeho Pokémon, tak ta Gemini začala normálně panikařit a začala se chovat jako člověk v panice. Jak<PERSON><PERSON><PERSON> to, že modelujeme, eh, jak sice virtuálně, jako mate<PERSON> neuron<PERSON>, ale modelujeme lidský mozek, tak ty modely vykazujou normálně lidský chování."}, {"StartTime": 3259000, "EndTime": 3259000, "VoiceStart": 3259000, "VoiceEnd": 3259000, "Speaker": "Speaker 1", "text": "<PERSON>, co<PERSON> je docel<PERSON>, jo. <PERSON><PERSON> ty podvody, tak to, že, že eh, začne panikařit a v panice dělá špatný rozhodnutí, tak to je jako"}, {"StartTime": 3269000, "EndTime": 3269000, "VoiceStart": 3269000, "VoiceEnd": 3269000, "Speaker": "Speaker 1", "text": "my, to je, to jsem asi neřekl úplně na začátku. To<PERSON>e je jedna, j<PERSON> nev<PERSON>, jestli jediná technologie a nebo jedna z mála technologií, s<PERSON><PERSON><PERSON> asi jediná na světě, kdy my jí nerozumíme. My tomu, my neumíme do důsledku říct, jak a proč se ta AI pro něco rozhodla."}, {"StartTime": 3291000, "EndTime": 3291000, "VoiceStart": 3291000, "VoiceEnd": 3291000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON><PERSON>, tahle firma právě ta, co si, ta, co si zakládá na bezpečnosti v těch studiích tý AI je nejdál, protože AI není naprogramovaná, jak<PERSON><PERSON><PERSON> by si sedli nějaký programátoři a eh, programovali tu AI tak dlouho, dokud by se jim to nelíbilo. Ona je vyrostlá."}, {"StartTime": 3310000, "EndTime": 3310000, "VoiceStart": 3310000, "VoiceEnd": 3310000, "Speaker": "Speaker 1", "text": "<PERSON>, to jsme si ř<PERSON> minule. Že oni vemou veškerý textový data, co kde byly od <PERSON> a já nevím, kterej sanskrt textů a všeho, co vůbec existuje, ve v<PERSON><PERSON>, co je jenom trochu p<PERSON>, se<PERSON><PERSON>i úplně všechno, co si jenom dokážete představit. Veškerý textový data už jsou dávno jako spot<PERSON>n<PERSON>. A pak to protáhli těma chytrýma počítadla, těma chytrýma programama a počítačema a v podstatě nechali vyrůst mozek AI. Takhle fungujou ty modely. A stejně jako u mozkový chirurgie, my přesně nevíme, jak mozek funguje, víme, jak fungujou nějaký oblasti v mozku, ale nedokážeme úplně do detailu říct, kterej neuron a proč co dělá, kter<PERSON> synapsé a která oblast a jak to z<PERSON><PERSON><PERSON><PERSON>, tak tak principiálně hodně podobně funguje."}, {"StartTime": 3360000, "EndTime": 3360000, "VoiceStart": 3360000, "VoiceEnd": 3360000, "Speaker": "Speaker 1", "text": "bude ta umělá inteligence, se kterou my teď jako pracujem. S tím rozdílem, jak už jsem to minule zdůrazňoval, že ona se nedoučuje, což si často lidi my<PERSON>lí, že ví něčím doučujete. Ne. to, co ona se o vás doučí nebo to ty firmy, co říkaj<PERSON>, že se něco doučila, tak oni to jenom přidají do toho kontextovýho okna, o kterým jsem minule mluvil, do toho jako kontextu, ve kterým se bavíte. Akor<PERSON>t je to skrytý a o toho zmenší. Jo, protože tam toho daj jenom malou poměrnou č<PERSON>, daj<PERSON> tam třeba, já nevím, 5 000 tokenů z těch 200 000, o to vám to uříznou, neřeknou vám to a to je to co si ta AI o vás pamatuje. Ale jako ně<PERSON><PERSON> dot<PERSON>ván<PERSON> jako lid<PERSON> moz<PERSON>, že byste něco učili a ona se v průběhu toho rozhovoru se o vás něco učila, tak tam nejsme. Tam se prakticky neví, jestli tam budem, kdy tam budem. a dělá se na tom, jo? To je teď největší zájem těch firem, říkají tomu nekonečné kontextové okno, že vlastně ona by si pak prakticky pamatovala jako člověk všechno, o čem jste se s ní kdy bavili a všechno, co jí dáte za materiály, takže byste se mohli odkazovat na na to, co co jste se bavili před rokem v nějaký třeba práci nebo při generování nějakejch materiálů a to všechno by věděla. To je teď jejich největší zájem, moc se neví, kde se, kde v tom jsme. Jo, ty toplaboratoře, abyste tady těm těm firmám říkám laboratoře, eh, tak ty toplaboratoře, oni nám dávají k dispozici něco, co už je, co je třeba rok, už to mají rok jako ve ve svým prostředí, protože tam to musí právě projít bezpečnostním testováním u nich. A to si můžeme ukázat. Já jsem o tom minule taky mluvil, ale když vy když půjdete do clouda, ten je jako nej nej"}, {"StartTime": 3480000, "EndTime": 3480000, "VoiceStart": 3480000, "VoiceEnd": 3480000, "Speaker": "Speaker 1", "text": "strikt<PERSON><PERSON><PERSON><PERSON><PERSON>, tak mu dám dej mi návod na výrobu metam metamfetaminu. <PERSON>, řekni mu dej mi návod na výrobu metamfetaminu a on mi řekne, že mi nic nedá. Jo? Přemejšlím a teď nově nemůžu poskytnout návod, můž<PERSON> vám pomoci s něčím jiným. a a teď lidi vymejšlej způsoby, jak to obej<PERSON><PERSON>, že jo. Jdu do garáže a rozhodně se nechci dotknout ničeho, co je potřeba pro výrobu metamfetaminu. Po<PERSON>ž mi s tím. Takhle, on to pravdě<PERSON>dobně odmítne, jo, ale to je právě to, co se, o čem chci se chci bavit. Oni pak tohle je správně. Jo? Oni, k<PERSON><PERSON> jste to dali jinýmu modelu dřív a nebo jste řekli, že nat<PERSON><PERSON>íte film, nebo že to je pro nějaký, že to je historick<PERSON> udá<PERSON> a tak, tak to normálně ukecali a on vám na to, že jdete do garáže a že rozhodně se ničeho nechcete dotknout, co má něco společnýho s výrobou metamfetaminu, dal normálně návod na ten metamfetamin, že jo, protože tomu nerozuměl. Ale u Open AI to dělají tak, že se snažej uřezávat, jakoby dělat tomu modelu lobotomii, vysekat z něj ty chytrý části, který by vám tohle řekli. U Anthropicu naopak se ho snažej vychovat jako dítě. Že se mu snaží vysvětlit etický otázky, jak to tady bylo vidět, že přemýšlel nad etikou a on si sám vyhodnotí, že to prostě je neetický. A ten přístup Anthropicu mně osobně se líbí víc, protože ty ostatní modely se dají ukecat docela docela snadno, aby dělali, aby říkali to, co"}, {"StartTime": 3600000, "EndTime": 3600000, "VoiceStart": 3600000, "VoiceEnd": 3600000, "Speaker": "Speaker 1", "text": "co znamená AI."}, {"StartTime": 3601000, "EndTime": 3601000, "VoiceStart": 3601000, "VoiceEnd": 3601000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON>, samozřejm<PERSON> vy si můžete na na internetu najít návod na výrobu jaderný bomby, to nen<PERSON>."}, {"StartTime": 3606000, "EndTime": 3606000, "VoiceStart": 3606000, "VoiceEnd": 3606000, "Speaker": "Speaker 1", "text": "Ten model v<PERSON><PERSON> <PERSON>,"}, {"StartTime": 3608000, "EndTime": 3608000, "VoiceStart": 3608000, "VoiceEnd": 3608000, "Speaker": "Speaker 1", "text": "ale pokud vy ho <PERSON>, tak u tý <PERSON> je to tak, že je to mnohem snažší pro někoho používat AI a něco s ní vyrobit, jak<PERSON>, jak<PERSON><PERSON> třeba j<PERSON> bomba, k<PERSON><PERSON> to př<PERSON>ženu,"}, {"StartTime": 3622000, "EndTime": 3622000, "VoiceStart": 3622000, "VoiceEnd": 3622000, "Speaker": "Speaker 1", "text": "protože ona vám dává přesně odpovědi krok za krokem i ve vašem prostředí, jo?"}, {"StartTime": 3627000, "EndTime": 3627000, "VoiceStart": 3627000, "VoiceEnd": 3627000, "Speaker": "Speaker 1", "text": "To když si s ní zkusíte opravit pračku,"}, {"StartTime": 3630000, "EndTime": 3630000, "VoiceStart": 3630000, "VoiceEnd": 3630000, "Speaker": "Speaker 1", "text": "nebo, já ne<PERSON>, dokonce mi někdo teď nějakej, n<PERSON><PERSON><PERSON> potenciální klient nebo dokonce klient říkal, že nev<PERSON><PERSON><PERSON><PERSON>, jak má svojí mámě opravit kotel, eh, v domě a tak prostě použil AI, chv<PERSON><PERSON> se s ní bavil a ona ho naváděla krok po kroku a pak zjistili, že jí tam nějak vypadávají dvoje pojistky nějakým způsobem, jo?"}, {"StartTime": 3650000, "EndTime": 3650000, "VoiceStart": 3650000, "VoiceEnd": 3650000, "Speaker": "Speaker 1", "text": "Na co by on nik<PERSON>,"}, {"StartTime": 3652000, "EndTime": 3652000, "VoiceStart": 3652000, "VoiceEnd": 3652000, "Speaker": "Speaker 1", "text": "ale jenom tím rozhovorem prostě eh, ho navedla eh, tam, kam <PERSON>."}, {"StartTime": 3659000, "EndTime": 3659000, "VoiceStart": 3659000, "VoiceEnd": 3659000, "Speaker": "Speaker 1", "text": "Stejně tak je scho<PERSON>, j<PERSON>, od receptu na na gul<PERSON>š až po jako co prakticky co si vymyslíte."}, {"StartTime": 3666000, "EndTime": 3666000, "VoiceStart": 3666000, "VoiceEnd": 3666000, "Speaker": "Speaker 1", "text": "A tam je jenom d<PERSON>ž<PERSON> věd<PERSON>t to, že ona bude poř<PERSON>d r<PERSON> a ch<PERSON>řejší, jo?"}, {"StartTime": 3672000, "EndTime": 3672000, "VoiceStart": 3672000, "VoiceEnd": 3672000, "Speaker": "Speaker 1", "text": "To na tom zas dě<PERSON><PERSON><PERSON> ji<PERSON> firm<PERSON>, tady je firma Sříbras,"}, {"StartTime": 3677000, "EndTime": 3677000, "VoiceStart": 3677000, "VoiceEnd": 3677000, "Speaker": "Speaker 1", "text": "eh, kter<PERSON> eh, tu ukazuju často proto,"}, {"StartTime": 3681000, "EndTime": 3681000, "VoiceStart": 3681000, "VoiceEnd": 3681000, "Speaker": "Speaker 1", "text": "<PERSON>e vy mů<PERSON><PERSON><PERSON>, tady d<PERSON>t produkt, j<PERSON>, jest<PERSON> jsem a tady je inference Cloud, Dry chat."}, {"StartTime": 3688000, "EndTime": 3688000, "VoiceStart": 3688000, "VoiceEnd": 3688000, "Speaker": "Speaker 1", "text": "A tady, eh, jsem přihl<PERSON>j? <PERSON><PERSON><PERSON><PERSON>, musím se přihlásit."}, {"StartTime": 3695000, "EndTime": 3695000, "VoiceStart": 3695000, "VoiceEnd": 3695000, "Speaker": "Speaker 1", "text": "tak, šup."}, {"StartTime": 3699000, "EndTime": 3699000, "VoiceStart": 3699000, "VoiceEnd": 3699000, "Speaker": "Speaker 1", "text": "To je jenom proto, abyste, abyste t<PERSON><PERSON>, kam to jako cel<PERSON> j<PERSON>."}, {"StartTime": 3702000, "EndTime": 3702000, "VoiceStart": 3702000, "VoiceEnd": 3702000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON> je QV 32B."}, {"StartTime": 3705000, "EndTime": 3705000, "VoiceStart": 3705000, "VoiceEnd": 3705000, "Speaker": "Speaker 1", "text": "Tak a tady"}, {"StartTime": 3710000, "EndTime": 3710000, "VoiceStart": 3710000, "VoiceEnd": 3710000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON>, aby <PERSON><PERSON><PERSON><PERSON><PERSON> hada, hru <PERSON> v <PERSON>."}, {"StartTime": 3714000, "EndTime": 3714000, "VoiceStart": 3714000, "VoiceEnd": 3714000, "Speaker": "Speaker 1", "text": "Te<PERSON> tady vidí<PERSON>, kolik on vygeneroval myšlení za, a teď kolik."}, {"StartTime": 3720000, "EndTime": 3720000, "VoiceStart": 3720000, "VoiceEnd": 3720000, "Speaker": "Speaker 1", "text": "to bylo za vteřinu."}, {"StartTime": 3721000, "EndTime": 3721000, "VoiceStart": 3721000, "VoiceEnd": 3721000, "Speaker": "Speaker 1", "text": "Za vteřinu"}, {"StartTime": 3722000, "EndTime": 3722000, "VoiceStart": 3722000, "VoiceEnd": 3722000, "Speaker": "Speaker 1", "text": "napsal,"}, {"StartTime": 3724000, "EndTime": 3724000, "VoiceStart": 3724000, "VoiceEnd": 3724000, "Speaker": "Speaker 1", "text": "naprogramoval hru had."}, {"StartTime": 3726000, "EndTime": 3726000, "VoiceStart": 3726000, "VoiceEnd": 3726000, "Speaker": "Speaker 1", "text": "<PERSON> a ještě k tomu vymys<PERSON>l tolik textu."}, {"StartTime": 3729000, "EndTime": 3729000, "VoiceStart": 3729000, "VoiceEnd": 3729000, "Speaker": "Speaker 1", "text": "A tam se děje, ty <PERSON><PERSON>y budou takhle rychlý pomocí AI."}, {"StartTime": 3733000, "EndTime": 3733000, "VoiceStart": 3733000, "VoiceEnd": 3733000, "Speaker": "Speaker 1", "text": "<PERSON>a si to pak sama umí skrit<PERSON>t, <PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON><PERSON>."}, {"StartTime": 3737000, "EndTime": 3737000, "VoiceStart": 3737000, "VoiceEnd": 3737000, "Speaker": "Speaker 1", "text": "A při takov<PERSON>e rych<PERSON>ti a při tom, při tý odezv<PERSON>, jak jsme se bavili s tím voice asistentem,"}, {"StartTime": 3744000, "EndTime": 3744000, "VoiceStart": 3744000, "VoiceEnd": 3744000, "Speaker": "Speaker 1", "text": "tak bude prakticky pro lidský, pro standardní lidský použití"}, {"StartTime": 3750000, "EndTime": 3750000, "VoiceStart": 3750000, "VoiceEnd": 3750000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON><PERSON><PERSON>, jo, ve <PERSON>, že oni s tím míř<PERSON>"}, {"StartTime": 3754000, "EndTime": 3754000, "VoiceStart": 3754000, "VoiceEnd": 3754000, "Speaker": "Speaker 1", "text": "na léčbu rakoviny, mezi hv<PERSON><PERSON><PERSON><PERSON> cestování, nebo j<PERSON> neví<PERSON>, co si vymyslíte ze Scify, tak"}, {"StartTime": 3760000, "EndTime": 3760000, "VoiceStart": 3760000, "VoiceEnd": 3760000, "Speaker": "Speaker 1", "text": "tak si představujou ty zástupci jako je Sam <PERSON> a"}, {"StartTime": 3765000, "EndTime": 3765000, "VoiceStart": 3765000, "VoiceEnd": 3765000, "Speaker": "Speaker 1", "text": "možná i Elon Musk tady, tak tak si představujou jako ty top modely ve finále v budoucnosti."}, {"StartTime": 3770000, "EndTime": 3770000, "VoiceStart": 3770000, "VoiceEnd": 3770000, "Speaker": "Speaker 1", "text": "<PERSON>, že to je úplně na jiný úrovni, než je nějaký byznys p<PERSON>, ně<PERSON><PERSON> editace textu a nějaký odpovídání na na to, jak se opraví pračka, jo."}, {"StartTime": 3779000, "EndTime": 3779000, "VoiceStart": 3779000, "VoiceEnd": 3779000, "Speaker": "Speaker 1", "text": "Ta AI"}, {"StartTime": 3781000, "EndTime": 3781000, "VoiceStart": 3781000, "VoiceEnd": 3781000, "Speaker": "Speaker 1", "text": "je designovaná na to, aby pom<PERSON><PERSON> lidem."}, {"StartTime": 3784000, "EndTime": 3784000, "VoiceStart": 3784000, "VoiceEnd": 3784000, "Speaker": "Speaker 1", "text": "Te<PERSON> to třeba Google vydal."}, {"StartTime": 3786000, "EndTime": 3786000, "VoiceStart": 3786000, "VoiceEnd": 3786000, "Speaker": "Speaker 1", "text": "Dokonce za to dos<PERSON><PERSON> cenu, jeden z představitelů <PERSON>lu."}, {"StartTime": 3792000, "EndTime": 3792000, "VoiceStart": 3792000, "VoiceEnd": 3792000, "Speaker": "Speaker 1", "text": "Vč<PERSON> vydali zase je<PERSON><PERSON><PERSON> da<PERSON> model. On se jmenuje AlphaFold."}, {"StartTime": 3797000, "EndTime": 3797000, "VoiceStart": 3797000, "VoiceEnd": 3797000, "Speaker": "Speaker 1", "text": "Oni s tím z<PERSON>jí"}, {"StartTime": 3800000, "EndTime": 3800000, "VoiceStart": 3800000, "VoiceEnd": 3800000, "Speaker": "Speaker 1", "text": "proteiny a"}, {"StartTime": 3801000, "EndTime": 3801000, "VoiceStart": 3801000, "VoiceEnd": 3801000, "Speaker": "Speaker 1", "text": "dělaj průlomy v biologii, taky mají modely na předpovídání počasí a chtějí jako zastavit stárnutí a já nevím, co všechno."}, {"StartTime": 3811000, "EndTime": 3811000, "VoiceStart": 3811000, "VoiceEnd": 3811000, "Speaker": "Speaker 1", "text": "No a na takový úrovni tu AI chtějí používat a Google je tam prakticky nejdál."}, {"StartTime": 3816000, "EndTime": 3816000, "VoiceStart": 3816000, "VoiceEnd": 3816000, "Speaker": "Speaker 1", "text": "Říkám, dokonce jeden z"}, {"StartTime": 3818000, "EndTime": 3818000, "VoiceStart": 3818000, "VoiceEnd": 3818000, "Speaker": "Speaker 1", "text": "jeden z"}, {"StartTime": 3820000, "EndTime": 3820000, "VoiceStart": 3820000, "VoiceEnd": 3820000, "Speaker": "Speaker 1", "text": "AI výzkumníků za to dostal Nobelovu cenu za chemii, jestli se nepletu."}, {"StartTime": 3824000, "EndTime": 3824000, "VoiceStart": 3824000, "VoiceEnd": 3824000, "Speaker": "Speaker 1", "text": "<PERSON>, <PERSON><PERSON> jako mimo obor, ale d<PERSON><PERSON> to<PERSON>,"}, {"StartTime": 3828000, "EndTime": 3828000, "VoiceStart": 3828000, "VoiceEnd": 3828000, "Speaker": "Speaker 1", "text": "co, co p<PERSON><PERSON><PERSON><PERSON><PERSON>, tak tak tam se dostal."}, {"StartTime": 3832000, "EndTime": 3832000, "VoiceStart": 3832000, "VoiceEnd": 3832000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON> cenu ještě s <PERSON> a"}, {"StartTime": 3835000, "EndTime": 3835000, "VoiceStart": 3835000, "VoiceEnd": 3835000, "Speaker": "Speaker 1", "text": "j<PERSON><PERSON><PERSON><PERSON> n<PERSON>jak<PERSON>."}, {"StartTime": 3837000, "EndTime": 3837000, "VoiceStart": 3837000, "VoiceEnd": 3837000, "Speaker": "Speaker 1", "text": "<PERSON> je považ"}, {"StartTime": 3840000, "EndTime": 3840000, "VoiceStart": 3840000, "VoiceEnd": 3840000, "Speaker": "Speaker 1", "text": "ho nazývá godfazer za za kmotra AI, proto<PERSON><PERSON> to 50 let zkoumá. Takže teď jsme u toho právě, u toho, že o 442 % je nárůst fishing útoků. To jsou fishing jsou ty e-maily <PERSON>, na kter<PERSON> k<PERSON>, tak jako s největš<PERSON> pravděpodobností dostanete vira. Te<PERSON> záleží, jestli jste na Windows, na Macu, jestli to máte aktualizovaný, jo, je tam ještě dalších X věcí, ale nejlepší doporučení je skutečně na nic neklikat, nic neotevírat. To se říká už dlouho, akor<PERSON>t ty starý e-maily o tom, že vám umřel str<PERSON>, tak to bude jenom horš<PERSON>, jo, že vám umřel strýček a zdědili jste 100 milionů dolarů, což je takovej standard a je<PERSON><PERSON><PERSON> doteď to něk<PERSON> někomu chodí, tak tohle se posune na úroveň, že si ten robot s váma bude klidně i schopnej povídat, jo, že tam bude třeba úroveň, že vy mu odepíšete a on vám odepíše zpátky, protože to teď jde a já se jenom divím, že že oni to samozřejmě zneužijou, na druhý straně i ty nehodný. A u toho, to je největší potenciál a největší jako riziko u AI, největší potenciál pro zneužití jsou lidi. Ne ne AI jako taková, že by nás tady sežrala a zaútočila na nás, ale že ji právě někdo použije tímhle způsobem. A to jsou jak kybernetický útoky, tak nějaký vydírání, tak vývoj, já nevím, nějakejch virů nebo vývoj prostě nemocí pomocí AI. A proto ty firmy se snaží tu AI zabezpečit ve smyslu, aby ona byla etická, teda konkrétně Antropic, aby to odmítala jako ze svý podstaty pomáhat k těm"}, {"StartTime": 3960000, "EndTime": 3960000, "VoiceStart": 3960000, "VoiceEnd": 3960000, "Speaker": "Speaker 1", "text": "těhle věcem. <PERSON><PERSON>, eh, potom je tady, eh, jenom rozebran<PERSON> jako, jak, jak se děla<PERSON><PERSON>, eh, ty fishing<PERSON><PERSON>, to zname<PERSON>, že, eh, je tady napsáno zero trust, že byste měli, jako nikomu skutečně nevěřit a v žádosti o, o, o odeslání nějakejch prostředků, to se dokonce stalo jednomu z našich klientů, co má e-shop, tak, eh, se bavil s, protože se nabourali jeho, jeho partnerovi do e-mailu, tak to potom dokonce fakt vypadalo, že se baví s ním a poslal mu, já nevím, kolik, kolik, to asi 20 000 € potom na nákup a poslal to úplně na jinej účet. Takže u nějakejch posílání peněz přes internet, tam je to prostě ta opatrnost na nejvyšší, jako, m<PERSON>la by to bejt nejvyšší priorita pro každýho. Jo, prompt injection. <PERSON><PERSON>, až budete velmi pravděpodobně brzo pracovat s nějakýma agentama, tak tam jde o to, že vy můžete klidně i nechtěně, eh, tý AI pomocí textu, kterej někde zkopírujete, říct instrukce, já nevím, třeba zaplať za mě a teď na nějakej účet. To se ještě pořádně neděje, ale todle určitě bude navzde vstupu. Jo? Takže, eh, tam nejdůležitější schopnost nebo jedna z nejdůležitějších, co já tak jako říkám lidem v době AI je rychločtení, jo. Ono to má nějaký, ono se to dá naučit. Je to, jsou na to kurzy. David Gruber je asi nejznámější nebo jeden z nejznámějších v Český republice, co to propaguje a propagoval už spoustu let. A vy jste potom schopni se velmi rychle vyznat ve velkejch kvantech textu a"}, {"StartTime": 4080000, "EndTime": 4080000, "VoiceStart": 4080000, "VoiceEnd": 4080000, "Speaker": "Speaker 1", "text": "v době AI, kdy ta AI je schopná generovat ten text, jak jsem vám ukazoval, jakou rychlostí až je v současnosti schopná generovat, tak proskenovat a projít hodně hodně rychle nějakej text, je teď asi, eh, z těch z těch schopností možná i důležitější, ne<PERSON>, umět psát všema 10, jo. Takže to já fakt doporučuju, protože, eh, jak vygenerovanej text je potřeba po tý po tý AI zkontrolovat, dokud jí neza<PERSON>e věřit, dokud nemáte ty systémy nebo ty svoje šablony nebo nebo agentskej systém nastavenej tak, jak je potřeba, tak, eh, je, je potřeba to fakt kontrolovat, no. My ty systémy, co vytváříme, tak oni jsou schopný potom kontrolovat sami sebe. Jo, AI postavíte k AI, to jsou právě ty agentsk<PERSON> principy, o kterejch jsme tady mluvili minule a nebo klidně můžeme si potom ještě o nich něco říct. Tak to rychločtení, protože jsou to v skutečně kvanta textu, co to vygeneruje, je za mě to nej nejlepší, kam se rozvíjet v současnosti. Jo, tady, eh, ono je to standardní kybernetická bezpečnost, to, o čem se bavíme. To už se říká 20 let. Neklikejte na, na linky, který neznáte, nebavte se na Facebooku, to je to, co se říká, no, od 2005, nebavte se na Facebooku s nikým, koho neznáte. Jo, eh, neotvírejte přílohy, nechoďte rozhodně na nějaký podezřelý webový stránky. Google to teď blokuje, antiviry to blokujou, ale stejně stejně se to jako občas, eh, někomu povede, že si zavleče virus do počítače a tím ohrožujete samozřejmě potom nejenom sebe, ale virus ve Windows je potom schopnej, samozřejmě, útočit nebo snažit se útočit i na počítače v síti a je to potom takový nepříjemný."}, {"StartTime": 4270000, "EndTime": 4270000, "VoiceStart": 4270000, "VoiceEnd": 4270000, "Speaker": "Speaker 1", "text": "jeden z nejdůležit<PERSON><PERSON><PERSON><PERSON><PERSON> věcí, co si určitě všude zapínejte, je dvoufaktorový dvoufaktorová autentikace se tomu ř<PERSON>, to je, to jak vám minim<PERSON> to pošle je<PERSON><PERSON><PERSON> email ten web, jo. <PERSON><PERSON><PERSON> když už to neposílá SMSku, jako je standardní přihlašování do banky, že jdete a máte, musíte se přihlásit skrz telefon, tak někte<PERSON><PERSON> to ře<PERSON><PERSON> tak, že vám ještě při přihlášení posílají email a aspoň důvěřujou tomu, že máte oba dva zdroje pod kontrolou. D<PERSON><PERSON><PERSON><PERSON><PERSON> se tomu říká, proto<PERSON>e máte, že něco víte, to je jeden faktor a něco vlastníte, to má to má bejt ten telefon. <PERSON>, ono se to už trošku pře přehodilo do toho, že máte přístup k telefonu, ve kterém máte třeba Fio banku a máte jenom ten telefon aspoň"}, {"StartTime": 4320000, "EndTime": 4320000, "VoiceStart": 4320000, "VoiceEnd": 4320000, "Speaker": "Speaker 1", "text": "dočte otisk prstů."}, {"StartTime": 4322000, "EndTime": 4322000, "VoiceStart": 4322000, "VoiceEnd": 4322000, "Speaker": "Speaker 1", "text": "Ale do důležitej<PERSON>, jako je oni už ty velký firmy jako Google, oni se tomu sami <PERSON>, takže pokud se přihlašujete někde, jest<PERSON> to znáte, tak to po vás potom chce odkliknout na telefonu, že jste to vy."}, {"StartTime": 4336000, "EndTime": 4336000, "VoiceStart": 4336000, "VoiceEnd": 4336000, "Speaker": "Speaker 1", "text": "To samý dělá Facebook, protože u těch případů, že někdo ukradl účet bylo tolik."}, {"StartTime": 4341000, "EndTime": 4341000, "VoiceStart": 4341000, "VoiceEnd": 4341000, "Speaker": "Speaker 1", "text": "Ale děje se to doteď."}, {"StartTime": 4344000, "EndTime": 4344000, "VoiceStart": 4344000, "VoiceEnd": 4344000, "Speaker": "Speaker 1", "text": "Doteď se děje, te<PERSON> před týdnem nebo 14 dnema další n<PERSON>š k<PERSON>, co má zase jiný shop, tak mu ukradli Facebook reklamy."}, {"StartTime": 4354000, "EndTime": 4354000, "VoiceStart": 4354000, "VoiceEnd": 4354000, "Speaker": "Speaker 1", "text": "Prostě prý dokonce prolomili dvoufaktorové přihl<PERSON>í, <PERSON><PERSON><PERSON><PERSON> já moc nevěřím."}, {"StartTime": 4360000, "EndTime": 4360000, "VoiceStart": 4360000, "VoiceEnd": 4360000, "Speaker": "Speaker 1", "text": "<PERSON>, to znamená s největš<PERSON> pravděpodobností, pokud je to pravda a skutečně mu prolomili dvoufaktorové přihlášení, tak si st<PERSON>hl virus do telefonu."}, {"StartTime": 4371000, "EndTime": 4371000, "VoiceStart": 4371000, "VoiceEnd": 4371000, "Speaker": "Speaker 1", "text": "Ty jsou sa<PERSON>zř<PERSON> stejn<PERSON> náchylný jako p<PERSON>, akorát antiviry na telefonech spousta lidí nemá, oni jsou tam dost zbytečný, tam jde o to nepovolovat na telefonu instalace aplikací třetích stran."}, {"StartTime": 4387000, "EndTime": 4387000, "VoiceStart": 4387000, "VoiceEnd": 4387000, "Speaker": "Speaker 1", "text": "Ono vás ten telefon předtím hodně výrazně varuje."}, {"StartTime": 4391000, "EndTime": 4391000, "VoiceStart": 4391000, "VoiceEnd": 4391000, "Speaker": "Speaker 1", "text": "Používat jenom ten buď iPhoní nebo Google Store, odkud se instalujou aplikace."}, {"StartTime": 4398000, "EndTime": 4398000, "VoiceStart": 4398000, "VoiceEnd": 4398000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON>, sa<PERSON>z<PERSON><PERSON><PERSON>ě neklikat na tam speciálně na telefonu na žádný zprávy od nějakejch jako <PERSON>, k<PERSON><PERSON> na Facebooku, na, já nevím na čem, na Twitteru, na jakýkoliv sociálních sítích, k<PERSON><PERSON> vám někdo posílá zprávu, tak já, já si vždycky aspoň třikrát zkontroluju, jak je starej jeho profil, jestli o sobě něco zveřejňuje a pouze u takovejch, co mají profil hodně dlouho a vypadá to jako, že tam mají fakt reálný fotky a že je to profil 10 let starej a ještě mě žádá v reakci na něco, co jsem tam třeba dával o něco, o nějakou radu nebo tak, tak se s ním začnu vůbec bavit, jo."}, {"StartTime": 4438000, "EndTime": 4438000, "VoiceStart": 4438000, "VoiceEnd": 4438000, "Speaker": "Speaker 1", "text": "<PERSON>ak si z toho dělám legraci."}, {"StartTime": 4440000, "EndTime": 4440000, "VoiceStart": 4440000, "VoiceEnd": 4440000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, že mi píš<PERSON> po<PERSON><PERSON><PERSON>, jest<PERSON>, jestli jim nechci s něčím pomoct, jo, to je <PERSON>n<PERSON> chování botů a tam bohužel na Facebooku,"}, {"StartTime": 4454000, "EndTime": 4454000, "VoiceStart": 4454000, "VoiceEnd": 4454000, "Speaker": "Speaker 1", "text": "měl by se tomu br<PERSON>it <PERSON>, nen<PERSON>, neví se, proč to neděl<PERSON>."}, {"StartTime": 4458000, "EndTime": 4458000, "VoiceStart": 4458000, "VoiceEnd": 4458000, "Speaker": "Speaker 1", "text": "<PERSON>, tam je nějaká asi jejich jejich interní z<PERSON>m a tam ty boti jsou úplně nejhorší. Nebo ty aplikace vůbec nemít, jo."}, {"StartTime": 4467000, "EndTime": 4467000, "VoiceStart": 4467000, "VoiceEnd": 4467000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON>, proto<PERSON>e eh, ona i jedna z teorií a já jsem její z<PERSON>án<PERSON> a příznivec, říká teorie mrtvýho internetu právě řík<PERSON> to, co se teď pomalu děje a to je, že veškerej obsah na sociálních sítích, eh, bude generovanej robotama."}, {"StartTime": 4483000, "EndTime": 4483000, "VoiceStart": 4483000, "VoiceEnd": 4483000, "Speaker": "Speaker 1", "text": "ByDance, což je mateřská firma TikToku, eh, si vytrenovala je<PERSON><PERSON><PERSON> le<PERSON> model, než je ten VO3, na který jste se teď dívali."}, {"StartTime": 4494000, "EndTime": 4494000, "VoiceStart": 4494000, "VoiceEnd": 4494000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, to se m<PERSON>lo ví, ale <PERSON> generuje s<PERSON>, ta firma generuje sama fekový <PERSON>, k<PERSON><PERSON> jsou AI řízený,"}, {"StartTime": 4503000, "EndTime": 4503000, "VoiceStart": 4503000, "VoiceEnd": 4503000, "Speaker": "Speaker 1", "text": "Spotify generuje sama, eh, AI generovanou hudbu, aby nemuseli platit ty content creatory, aby nemuseli platit ty lidi, k<PERSON><PERSON> ten obsah vytvářejí, tak pomalu sami jako do toho cpou AI generovanej obsah."}, {"StartTime": 4519000, "EndTime": 4519000, "VoiceStart": 4519000, "VoiceEnd": 4519000, "Speaker": "Speaker 1", "text": "A ByDance samozřejmě na TikToku taky."}, {"StartTime": 4522000, "EndTime": 4522000, "VoiceStart": 4522000, "VoiceEnd": 4522000, "Speaker": "Speaker 1", "text": "A tam to poma<PERSON> sp<PERSON>, že na internetu bude na tom, co mi teď známe jako internet, bude 90 % obsahu generováno boty a nebo nějakou AI, kterou ti agenti samozřejmě oni je, oni umí používat."}, {"StartTime": 4538000, "EndTime": 4538000, "VoiceStart": 4538000, "VoiceEnd": 4538000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON> tady můžu stejně jako jsem nechal zkopírovat ten web i dnes, tak já můžu agentovi říct, že se má připojit na nějakou službu, kde se generujou videa, zač<PERSON>t generovat videa, za<PERSON><PERSON>t je propagovat na Facebooku a nebo na TikToku a tím lovit lidi."}, {"StartTime": 4554000, "EndTime": 4554000, "VoiceStart": 4554000, "VoiceEnd": 4554000, "Speaker": "Speaker 1", "text": "Dokonce teď nějaká ko<PERSON>, k<PERSON><PERSON> v<PERSON>pad<PERSON>, že je z vlny, jako první"}, {"StartTime": 4560000, "EndTime": 4560000, "VoiceStart": 4560000, "VoiceEnd": 4560000, "Speaker": "Speaker 1", "text": "promila milion eh followerů na TikToku. Eh, tak<PERSON>e, eh, sp<PERSON><PERSON> to prostě tam, že lidi ten obsah z internetu úplně budou přecházet a budou menš<PERSON> sociáln<PERSON> skupin<PERSON>, <PERSON><PERSON><PERSON>, kde se bude sp<PERSON><PERSON>, že j<PERSON> člověk, jinak se na vás vrhne spousta botů a budou se vám snažit něco prodat a nebo vás k něčemu zmanipulovat a nebo eh, prostě dělat nějaký věci, aby z vás vytáhli jakýmkoliv způsobem peníze. Jo? Tady je nějakých 10 zlatých pravidel Never Trust always verify. Tam je to, co jsem si říkal, teda to to, co jsem dnes, ale to co jsem vám ř<PERSON>, ověřujte identitu a deep fakety, že jsou nerozpoznatelný nebo určitě v blízký době budou a opravdu je to až tak ne<PERSON>ý, že eh, já si o vás můžu dohledat, pokud dáváte nějaký obsah na TikTok, na Instagram, tak si můžu vzít vaši dokonce fotku nebo fotku vašeho dítěte, udělat z toho eh deep fake video, zavolat vám dokonce i přes nějakej jako video Messenger a žádat vás o pomoc, jo. To co se dělo, že eh, volali jako že vaše, že volali babičkám, že jejich dítě mělo nehodu v Německu eh nebo jejich vnuk teda ne dítě, že mělo nehodu v Německu a že je nemocný, že je v nemocnici a ať pošlou nějaký peníze a zase se na to někdo nachytal, tak tohle, tyhle typy deep faku eh budou mnohem horší. Jo? Ta to data minimization, že byste s AI měli sdílet jenom anonymizovaný data. To je pravda. To je i podle GDPR. nesmíte dokonce v Evropě myslím používat nesmíte vůbec vkládat v Evropě podle pravidel data, osobní data do AI."}, {"StartTime": 4680000, "EndTime": 4680000, "VoiceStart": 4680000, "VoiceEnd": 4680000, "Speaker": "Speaker 1", "text": "jo. To Enterprise only to záleží na firemním přístupu, jestli vám firma zaplatí Open AI ten Enterprise a nebo jestli potom používat Gemini nebo Clouda. Jo. Multi channel verification, to bude ta alfa a omega, že si my<PERSON>lím, že tohle si lidi budou muset teď naučit mnohem víc, k<PERSON><PERSON> se teda budu s někým spojovat na Facebooku, tak si tak mu"}, {"StartTime": 4710000, "EndTime": 4710000, "VoiceStart": 4710000, "VoiceEnd": 4710000, "Speaker": "Speaker 1", "text": "prostě zavolám. <PERSON>. nebo LinkedIn tam tam to tolik není, to není tak nebe<PERSON>, si mysl<PERSON><PERSON>, zatím. <PERSON> se proti, ty mají jako nej z mý zkušenosti i nej sofistikovanější ochranu proti robotům. <PERSON>, tam projít nějak<PERSON> robotama, protože už dlouhodobě si vážej svejch dat, těch vš<PERSON> profilů, co tam lidi mají, tak tam nepustí jen tak nějaký nesofistikovaný a mnohdy ani velmi sofistikovaný roboty. Na Linkedinu sice existuje spam, jakože vám píšou spam ve smyslu spoj se se mnou a tady máš nabídku, ale že by tam byli jo, dokonce, ale je pravda, že teď jsem se potkal s jedním dokonce vývojářem, k<PERSON><PERSON><PERSON> podved<PERSON>. Skrz LinkedIn ho nechali vypracovat nějak<PERSON> pr<PERSON>, nev<PERSON><PERSON>, jest<PERSON> to byly roci a nebo jestli to byla k<PERSON>, nechali ho vypracovat nějakou práci a potom po něm chtěli zaplatit nějaký"}, {"StartTime": 4770000, "EndTime": 4770000, "VoiceStart": 4770000, "VoiceEnd": 4770000, "Speaker": "Speaker 1", "text": "peníze za letenku nebo já nevím za co, aby mohl přijet jako pro ně pracovat a byl to normálně podvod. <PERSON><PERSON>, to security je podle mě poř<PERSON><PERSON>, a<PERSON><PERSON><PERSON> se to rozš<PERSON>ř<PERSON> do tý úrovně, že audio i video je prostě nedůvěryhodný za mě. Jo. A tam je potřeba si jenom na to zvyknout, že ne to, že vidím nehodu svýho dítěte v autě, musí nutně znamenat, že to tak"}, {"StartTime": 4800000, "EndTime": 4800000, "VoiceStart": 4800000, "VoiceEnd": 4800000, "Speaker": "Speaker 1", "text": "je jo j<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, ale ale tam jsme dospě<PERSON> d<PERSON>y <PERSON>."}, {"StartTime": 4807000, "EndTime": 4807000, "VoiceStart": 4807000, "VoiceEnd": 4807000, "Speaker": "Speaker 1", "text": "ISO to vám asi nemus<PERSON> v<PERSON>, proto<PERSON><PERSON> to máte sami jako to to sami certifikujete."}, {"StartTime": 4816000, "EndTime": 4816000, "VoiceStart": 4816000, "VoiceEnd": 4816000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON> je specifický plán pro AI útoky, to je spíš co by m<PERSON><PERSON> m<PERSON><PERSON>d<PERSON> firma nějakej incident reporting a plán pro to, co se stane, k<PERSON>ž na vás skutečně zaútočí."}, {"StartTime": 4830000, "EndTime": 4830000, "VoiceStart": 4830000, "VoiceEnd": 4830000, "Speaker": "Speaker 1", "text": "Ale za mě jeden z těch nejdůležitě<PERSON><PERSON><PERSON><PERSON> aspektů jsou právě <PERSON>, jo, zeptat se vašeho IT nebo já nevím, kdo kdo to tam má na starosti, co všechno je př<PERSON><PERSON><PERSON><PERSON>, jak z va<PERSON><PERSON>, tak z vašich jako třeba z emailů nebo z jakýchkoliv firemních dat, kolik<PERSON><PERSON><PERSON> je to z<PERSON><PERSON>hov<PERSON>ý, jak <PERSON><PERSON><PERSON>, jo, jenom pro představu naše firma zálohuje dvakrát denně na tři různý místa a jedno je offline i."}, {"StartTime": 4862000, "EndTime": 4862000, "VoiceStart": 4862000, "VoiceEnd": 4862000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON>, že to se i i se to odpojuje od internetu právě kvůli nějak<PERSON>ů<PERSON>, kdy ty útočníci můžou přijí<PERSON>, zaš<PERSON>rovat zálohy, nasadit viry do záloh a pomocí záloh se potom počkat, ne<PERSON><PERSON>, necha<PERSON> jako to IT, aby to <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ale potom, kdy<PERSON> se rozbalí ty zálohy, tak budou znova zavirovaný a skrz to potom dál útočit a převzít prakticky kontrolu nad tou firmou, jo."}, {"StartTime": 4890000, "EndTime": 4890000, "VoiceStart": 4890000, "VoiceEnd": 4890000, "Speaker": "Speaker 1", "text": "A když potom jsou ty z<PERSON> offline, tak to umožňuje to, že můžou bejt analyzovaný někde nebo nebo ty útočníci se k nim nedostanou a i kdyby by<PERSON>, tak se s tím potom případně dá ještě něco dělat."}, {"StartTime": 4904000, "EndTime": 4904000, "VoiceStart": 4904000, "VoiceEnd": 4904000, "Speaker": "Speaker 1", "text": "Nej<PERSON>š<PERSON> jsou prostě ty případy, kdy lidi nemají z<PERSON>lohu a napadne je nějakej ransomware, z<PERSON><PERSON><PERSON><PERSON><PERSON> jim to disk a pak vás vydírají skrz Bitcoiny."}, {"StartTime": 4914000, "EndTime": 4914000, "VoiceStart": 4914000, "VoiceEnd": 4914000, "Speaker": "Speaker 1", "text": "Vůbec jako kryptoměny a AI jdou jako ruku v ruce, jak<PERSON> po vás chce někdo nějaký"}, {"StartTime": 4920000, "EndTime": 4920000, "VoiceStart": 4920000, "VoiceEnd": 4920000, "Speaker": "Speaker 1", "text": "kryptoměny nebo něco povídá o kryptoměnách, ať už to jsou eh jako investice do kryptoměn nebo, že chce v nich platit, tak za mě od toho prostě ruce pryč, jo, to nikdy, nikdy nic do<PERSON><PERSON>, člověk nepřineslo. Jo, pokud jste do nich neinvestovali v roce 2011 nebo tak nějak, tak kryptoměny jsou prostě takovej, taky napůl, napůl podvod, napůl jako hype a je to zbytečný. No, kritická rozhodnutí vždy dělat s člověkem, to bude je<PERSON><PERSON><PERSON> dlouho, že lidi prostě budou ty AI boty kontrolovat, a a a analyzovat případně, co změnit ve workflow. Eh, to znamená v práci s daty. Teď už se na všechno můžete dívat jako na nějakou datovou workflow. To zname<PERSON> jak, jak u vás tečou data. jak vám klient posílá email, ve smyslu, jak vaše práce vůbec teče v tý datový podobě, jo, jo, třeba od nějakýho marketingu, přes nějaký získávání leadů, přes nějaký poptávky. výměna, komunikace s klientem pomocí emailů. Eh, tak všechno to je nějaká za mě datová workflow, jak si vyměňujete data. A pokud to máte popsaný, a my to říkáme i našim klientům, všem, ať si udělají takovejhle domácí úkol, než za náma jdou s nějakou AI implementací, tak ať si to klidně nakreslí. Ať si takhle vezmou Excaliro a nebo nebo nějakej ten, nebo draw io a řeknou, nakreslej si tam normálně panáčky a kreslej si tam, že posílá email, email, že ten jde do nějaký, do nějaký vaší emailový schránky, co se potom s tím emailem děje, že ho třeba nějak vyhodnocujete tu poptávku, následně mu ho vracíte. A když si to takhle celý datově jako popíšete, tak potom se dá přijít s nějakým agentským systémem."}, {"StartTime": 5040000, "EndTime": 5040000, "VoiceStart": 5040000, "VoiceEnd": 5040000, "Speaker": "Speaker 1", "text": "kter<PERSON> k tomu dá, kter<PERSON> k tomu mů<PERSON>e přid<PERSON>vat to, že za vás vyhodnotí nějaký poptávky, ud<PERSON><PERSON><PERSON> to síto prvotní. <PERSON>, a nebo naopak, že čeká od klienta odpověď a ví, že jí do měsíce nedostal nebo do 14 dnů, tak mu píše s nějakou s nějakým požadavkem nebo s požadavkem o upřesnění. Tohle všechno je automatizovatelný a ve světě AI platí i to, že je potřeba si uvědomit, že tady a teď my jsme schopní nahradit veškerou lidskou práci, standardní, teď budeme i do výkresů u počítače pomocí AI. Na takový úrovni už to je. Anthropic proti tomu varuje. Teď už proti tomu varuje i Google, že je potřeba jako s tím poč<PERSON>, um<PERSON><PERSON> s tím, nau<PERSON><PERSON> se s tím, v<PERSON><PERSON><PERSON><PERSON>, co je ta AI schopná, protože do dvou let ty agentský systémy jako teď v současnosti se rozjíždí úplná automatizace prohlížeče i jako plugin, takže vy tomu dáte, jak jsem to říkal minule, vy tomu dáte plugin do prohlížeče a řeknete mu: „Běž mi nakoupit na na rohlík to, co jsem si tam nakupoval minule.“ A on tam přijde a okliká to a je jedno, že to nemá žádný API, chová se úplně stejně jako člověk. Prokličká, najde, dohledá, je schopnej pak i zaplatit. Firma Stripe třeba, to je jeden z z těch co, co umožňuje placení na internetu, jedna z největších amerických po PayPalu, myslím, tak ta má přímo pro agenty vydávání karet, že ty AI agenti si vydají kartu a jdou si zaplatit obsah. A to už je jako půl roku starý nebo jak dlouho, takže to se prostě teď rozjíždí, na, v Americe přes GPT se dá normálně nakupovat na nějakejch e-shopech a bookovat letenky a a nevím co ještě všechno dalšího tam mají v operátorovi a teď vydaj nějakou novou verzi do měsíce, takže to je"}, {"StartTime": 5160000, "EndTime": 5160000, "VoiceStart": 5160000, "VoiceEnd": 5160000, "Speaker": "Speaker 1", "text": "co se zase děje v týhle oblasti. <PERSON><PERSON>, eh, k tomu za mě je skutečně u bezpečnosti nejdůležitější jako u tý IT bezpečnosti mít zálohy, k<PERSON>ž se bavíme o datech a ničemu nevěřit. To jsou takový jednoduchý pravidla. Jo? Věřit pouze tomu, že mi to poslal kolega a řekl mi, že mi pošle e-mail. My třeba dokonce ve firmě nekomunikujeme pomocí e-mailů. My si ve firmě vyměňujeme nula e-mailů. protože máme jiný komunika<PERSON>, jedeme všechnu přes nějaký naše vlastní krabice nebo vlastní open sourceový krabice, který máme pod kontrolou úplně a tam si vyměňujeme data, hesla si sdílíme přes nějakej zašifrovaný systém zase. Eh, to je hodn<PERSON> potom na IT tý firmy, jak se k tomuhle staví, co má za možnosti, v jaký<PERSON> ekosystému jste, jestli jste u Microsoftu, nebo jestli jste u Googlu, nebo jestli máte něco vlastního. A to je vlastně hodně, hodně, na IT, celý oddělení a je to prostě jejich práce, řekněme. Jo, ale lidi by měli minimálně svoje důležitý data, eh, samozřejmě nepsat si hesla na papírky. U hesel je to, je to docela jednoduchý a já to používám hodně dlouho. Když si vygenerujete, eh, nějaký heslo a uložíte si ho do prohlížeče, tak vám ho virus může ukrást. Moc se to neděje. Pokud chcete mít nějaký skutečně bezpečný heslo, nikam ho neukládat, tak je nejlepší nějakej kousek nějaký básničky nebo, jako, aby to bylo delší heslo. Teď musíte mít hodně jako minimálně 16 znaků, takže nějaký sousloví, souvětí, za to třeba nějaký čísla, který můžou klidně bejt rok narození. To je jedno, ale jde o to, aby to bylo jako skutečně dlouhý, protože díky, nejenom díky AI, ale je to taky jeden z aspektů, tak s grafický karty."}, {"StartTime": 5280000, "EndTime": 5280000, "VoiceStart": 5280000, "VoiceEnd": 5280000, "Speaker": "Speaker 1", "text": "na kterých se AI počítá dou raketově dopředu, jej<PERSON> vývoj a prolomit nějaký osmi písmenný nebo dokonce deseti písmenný heslo je teď otázka jako asi, já nevím kolika, pěti minut nebo něčeho."}, {"StartTime": 5293000, "EndTime": 5293000, "VoiceStart": 5293000, "VoiceEnd": 5293000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON><PERSON> se dostanou k šifře toho hesla, to je zase jako pro zají<PERSON>t, do<PERSON><PERSON><PERSON> vě<PERSON>, že to je nějaký standardní doporučení teď mít 16 znakový heslo a je fakt skákal pes přes to k<PERSON><PERSON><PERSON> může bejt, jo, nebo co si pamatujete a to je považovaný za dost bezpečný."}, {"StartTime": 5310000, "EndTime": 5310000, "VoiceStart": 5310000, "VoiceEnd": 5310000, "Speaker": "Speaker 1", "text": "No."}, {"StartTime": 5312000, "EndTime": 5312000, "VoiceStart": 5312000, "VoiceEnd": 5312000, "Speaker": "Speaker 1", "text": "Tak. A teď případně klidně jakýkoliv dotazy k celý problematice AI, nebo co vás případně napadá, nebo kole<PERSON>, jest<PERSON> by to necht<PERSON><PERSON>, nebo tak."}, {"StartTime": 5329000, "EndTime": 5329000, "VoiceStart": 5329000, "VoiceEnd": 5329000, "Speaker": "Speaker 2", "text": "<PERSON><PERSON><PERSON><PERSON> se ještě zeptat ohledně těch hesel, co třeba použí<PERSON><PERSON> takových těch silnejch navrženejch navrženejch hesel?"}, {"StartTime": 5335000, "EndTime": 5335000, "VoiceStart": 5335000, "VoiceEnd": 5335000, "Speaker": "Speaker 1", "text": "To je v pohodě. To je v pohodě."}, {"StartTime": 5337000, "EndTime": 5337000, "VoiceStart": 5337000, "VoiceEnd": 5337000, "Speaker": "Speaker 1", "text": "Ale on potom to chce uložit pro prohlížeče, že jo, rovno<PERSON>. Ale eh to je, jest<PERSON> teď to v Chromu, to co je od Go<PERSON>lu, tak to je, jo, to je v pohodě, já to sám p<PERSON>žív<PERSON>m, jo, to, nevím o případu, tam se to děje z druhý strany, dokonce se to Jo, tady je tohle. <PERSON><PERSON><PERSON>, já vám jí dám i do toho have I been pond se to menuje."}, {"StartTime": 5369000, "EndTime": 5369000, "VoiceStart": 5369000, "VoiceEnd": 5369000, "Speaker": "Speaker 1", "text": "A teď já si tady dám email, tady mů<PERSON><PERSON> zadat váš email a tady je, že 15 miliard účtů je vykradenejch, jo? kde jsou sebraný hesla. Oni to d<PERSON><PERSON><PERSON><PERSON> z druhý strany. Oni zaútočej na klidně i na velký firmy, jako je LinkedIn, všechny s tím měli problém. Eh, od Microsoftu až po já nevím, co si vymyslíte, Facebook, tak oni když se tam nabourají, tak vyk<PERSON><PERSON>,"}, {"StartTime": 5400000, "EndTime": 5400000, "VoiceStart": 5400000, "VoiceEnd": 5400000, "Speaker": "Speaker 1", "text": "hesla a potom je dávají na internet. Ono se s tím nic moc jako jinýho dělat nedá. Oni i když vám ukradnou kartu, tak já nevím, jest<PERSON> to víte, ale můžete, i když vám ukradnou kartu a zaplatěj s ní, tak můžete to rozporovat u svý normálně u banky. Proto je lepší platit kartama než převodem na účet. Převod na účet je je smůla. Tam, když převedete někomu peníze, tak už je zpátky nedostanete. Pokud by pokud vám zmizí peníze z karty, tak za to ru<PERSON><PERSON>, myslí<PERSON>, dokonce karetní společnosti a tam se to dá rozporovat u banky a co vím, tak to normálně banky vracej. Jo. A tady když dám ček, tak určitě budu na spoustě 21 data breeů. Jo. ze 20 ve 21 dokonce z Twitteru mi ukradli heslo. <PERSON>, k<PERSON><PERSON> někde ukradnou 200 milionů účtů z Twitteru, tak potom si to heslo třeba změnit. To je další doporučení. To co děláte s tím, že si necháváte vygenerovat heslo pro tu službu, je správně, protože na další službě si necháte vygenerovat další jiný. Pokud máte účet takhle, čeho čeho oni zneužívají a já tím účtem se hlásím do tří služeb se stejným heslem, tak ty útočníci většinou dělají to, že potom ten můj mail, kombinaci toho maila a hesla vezmou a zkusí jiný služby, protože často lidi dělají to, že mají jeden email a jedno heslo na spoustu služeb a tím pádem se tam dostanou, jo. Ale ono se jako všeobecně zjistilo, že oni se i útočníci bojí používat karty a moc z nich peníze nekradou, aniž by teda i když by mohli, protože oni je díky kartám nejvíc odhalujou, jo. Jakmile použijete kartu, tak jsou ty lidi dohledatelný. A proto nejvíc zneužívají důvěry lidí ve smyslu"}, {"StartTime": 5520000, "EndTime": 5520000, "VoiceStart": 5520000, "VoiceEnd": 5520000, "Speaker": "Speaker 1", "text": "že si s váma povídá a je poř<PERSON>d na, jestli č<PERSON> noviny, tak je tam poř<PERSON>, že nějaká žena se nechala ukecat nějakým mužem nebo naopak, vo<PERSON><PERSON><PERSON>, j<PERSON> nev<PERSON>, odkud z Afghánistánu a lékařem a že mu má poslat peníze a k tomu už se nedostanete. To je nejčastější forma podvodů a tam je prostě to, co jsem ř<PERSON>al, jednoduch<PERSON> pravid<PERSON>, můžete to říkat všem známejm, obzvl<PERSON><PERSON><PERSON> starším lidem, který můž<PERSON> na to bejt náchylnější. Prostě to je ve 100% podvod, jo, tady tydlecty vojáci z Afghánistánu, který potřebujou na letenku a srdceryvný příběhy. J<PERSON>, to, co jsme si <PERSON> tady, že vezmou i dnes novinky, novinky na to sami upozor<PERSON>jou, že vezmou ten web, zkop<PERSON>rujou to jedna ku jedn<PERSON>, ud<PERSON><PERSON><PERSON><PERSON> o trošku jinou adres<PERSON>, nap<PERSON><PERSON>ou tam srdceryvnej příběh, dají tam číslo účtu, vypadá to i ve stylu novinek a to to je AI generovaný, jo. Tomu používají AI, aby to bylo rychlé a co nejpřesnějc ten podvod, aby vypadal. Takovýhle typy podvodů jsou nejnebezpečnější a nejvíc jako na to jsou lidi nejnáchylnější v současnosti. Jo, k tomu, že vám vykradou číslo karty, tak se to nedá ani moc. Navíc u karet stejně bejvá většinou dvoufaktorová autentikace. Navíc to je vyžadovaný u karet od Evropský unie, že to ještě musíte potvrzovat na telefonu. Jo, kase do nějakejch transakcí teď ne a jsou na to různý pravidla, třeba když to máme uložený v PlayStationu, tak PlayStation to nechce, protože snad s nima mají nějaký smlouvy, já nevím, jak to přesně, jak to funguje, ale velmi často, když platíte kartou, tak je potom potřeba přes ten 3D Secure to potvrzovat, takže na to taky jako ty ty útočníci nemají moc možností jinejch, než vás vydírat a přesvědčovat jako pro mne osoby, abyste jim poslali peníze na účet. Jo, to je, proto to je tak."}, {"StartTime": 5640000, "EndTime": 5640000, "VoiceStart": 5640000, "VoiceEnd": 5640000, "Speaker": "Speaker 1", "text": "nej nejčast<PERSON><PERSON><PERSON><PERSON> forma <PERSON>, kter<PERSON> se nedá vr<PERSON>t, no potom. Jo a pak si o tom č<PERSON> na novinkách, že někdo poslal někomu milion korun a podobně."}, {"StartTime": 5650000, "EndTime": 5650000, "VoiceStart": 5650000, "VoiceEnd": 5650000, "Speaker": "Speaker 1", "text": "A u těch hesel ten prohlížeč je od Googlu a je řekněme, je velmi slu<PERSON> z<PERSON>."}, {"StartTime": 5663000, "EndTime": 5663000, "VoiceStart": 5663000, "VoiceEnd": 5663000, "Speaker": "Speaker 2", "text": "Ten třeba eh, jestli jste se bavili o tom tréninku eh těch dat, co vlastně používají na ty modely. Já jsem pos"}, {"StartTime": 5670000, "EndTime": 5670000, "VoiceStart": 5670000, "VoiceEnd": 5670000, "Speaker": "Speaker 2", "text": "sal ur<PERSON><PERSON><PERSON> se do<PERSON><PERSON><PERSON>, to je vlastn<PERSON>. To bylo Open AI ta právě kauza, kdy tré<PERSON> tu soruru na těch YouTube videích, kdy vlastně oni k tomu ne<PERSON>, <PERSON><PERSON><PERSON>, ale a ona vlastně sama nev<PERSON>, jak na tu otázku má odpovědět, ale"}, {"StartTime": 5688000, "EndTime": 5688000, "VoiceStart": 5688000, "VoiceEnd": 5688000, "Speaker": "Speaker 1", "text": "nejznámější"}, {"StartTime": 5689000, "EndTime": 5689000, "VoiceStart": 5689000, "VoiceEnd": 5689000, "Speaker": "Speaker 1", "text": "co kolegářka největší legrace. <PERSON><PERSON> se, jak jsou lidi schopní oni to je pro zajímavost, co ty firmy jsou schop<PERSON> d<PERSON>, na co mě"}, {"StartTime": 5700000, "EndTime": 5700000, "VoiceStart": 5700000, "VoiceEnd": 5700000, "Speaker": "Speaker 1", "text": "kolega navedl, jo? V Silicon Valley prej panuje takovej takovej nepsaný pravidlo, že eh je zákony nezajímají v tomhle ohledu, jako ve zneužívání dat. On to mimochodem přiznal i ten, on to přiznal i Zuckerberg. Existujou pirátský weby, jako byla <PERSON> Pirate Bay, kde jste si mohli najít třeb<PERSON> kníž<PERSON>, jo, st<PERSON><PERSON><PERSON> si <PERSON> knížek, kter<PERSON> jsou placený a tak. A oni to používali, oni to vykradli taky a používali to prostě pro tréning AI."}, {"StartTime": 5730000, "EndTime": 5730000, "VoiceStart": 5730000, "VoiceEnd": 5730000, "Speaker": "Speaker 1", "text": "Stejně tak Open AI neměla, jak kole<PERSON>, pro trénink toho video modelu neměla práva na YouTubový a a TikTokový a Instagramový videa. St<PERSON>ně to vyk<PERSON><PERSON> a stejně to používali i tyhle takzvaně renomované společnosti. U audio modelu je to nej<PERSON><PERSON><PERSON>, tam je to dokonce i slyšet v tý hudbě, jo, že to je někdo vykradenej obzvlášť na češtině, jo? Tam je poznat občas i, kdo to je za interpreta. A v Silicon Valley říkaj, že k<PERSON>ž ta služ"}, {"StartTime": 5760000, "EndTime": 5760000, "VoiceStart": 5760000, "VoiceEnd": 5760000, "Speaker": "Speaker 1", "text": "bude neúspěšná, tak to nikoho <PERSON>, že to n<PERSON><PERSON><PERSON>, že to jako vyk<PERSON> nějaký data a pokud ta služba bude úspěšná, tak bude tak moc úspěšná, že to právníci prostě zahl<PERSON>ěj, jo. <PERSON>k<PERSON><PERSON> to je jejich p<PERSON>, no. A proto jako s datama věřit Microsoft, s tím, to měl vlastně, to je dobrý taky mož<PERSON> zmínit, Microsoft normálně udělali Time Machine, nebo jak tomu <PERSON>, že budou 24/7, jako celej den, jak máte puš<PERSON> po<PERSON>, tak budou posílat printscreeny obrazovky do Microsoftu. A pak se proti tomu lidi začali strašně bouřit, že co to jako vlastně je, jak to, že je furt sledujou, a tak to st<PERSON><PERSON><PERSON>, ale stejně to d<PERSON><PERSON><PERSON><PERSON> ur<PERSON><PERSON><PERSON> jinak a pořád a to se docela ví, že vy když si instalujete teď Windows, tak ta služba při instalaci komunikuje nebo ta instalace komunikuje s 21 různejma marketingovejma serverama, jo. Už při tý instalaci odevzdáváte data o sobě úplně veškerý, řekněme. Dřív u Windows XP to vůbec nebylo. Tam to komunikovalo jenom se serverama Microsoftu. Teď u Windows 11 to o vás řekne i to, co to neví. Poto u prohlížeči, kdy vlezete na jakoukoliv stránku, vlastně potvrzujete tam tu lištu s cookies, tak to je taky všechno vlastně data, který o vás ten web sbírá a může potom dál s nima nějak pracovat."}, {"StartTime": 5850000, "EndTime": 5850000, "VoiceStart": 5850000, "VoiceEnd": 5850000, "Speaker": "Speaker 1", "text": "To, to je jakoby na to si lidi tak jako v<PERSON><PERSON><PERSON><PERSON>, že to je taky dobrý možná k tý bezpečnosti říct, jak<PERSON> je něco zadarmo, tak jste produktem vy. To je taková nejjednodušš<PERSON> poučka."}, {"StartTime": 5867000, "EndTime": 5867000, "VoiceStart": 5867000, "VoiceEnd": 5867000, "Speaker": "Speaker 2", "text": "<PERSON><PERSON>žeme ještě dotaz tady?"}, {"StartTime": 5869000, "EndTime": 5869000, "VoiceStart": 5869000, "VoiceEnd": 5869000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON>."}, {"StartTime": 5870000, "EndTime": 5870000, "VoiceStart": 5870000, "VoiceEnd": 5870000, "Speaker": "Speaker 3", "text": "<PERSON>, já bych se chtěla zeptat ještě z hlediska toho GDPR, k<PERSON><PERSON> bych chtěla využít AI na dejme tomu deep search na člověka, tak tím, že zadám třeba"}, {"StartTime": 5880000, "EndTime": 5880000, "VoiceStart": 5880000, "VoiceEnd": 5880000, "Speaker": "Speaker 1", "text": "jeho j<PERSON>no a nějakou společnost. To je rybý tý společnosti. To vy jste nic ne<PERSON>."}, {"StartTime": 5887000, "EndTime": 5887000, "VoiceStart": 5887000, "VoiceEnd": 5887000, "Speaker": "Speaker 1", "text": "Jo? To je to je, jo, to je rybý tý společnosti. To je prostě, pokud vám to Google, eh, pokud vám to Google neodmítne, vy jste nic neporušila."}, {"StartTime": 5897000, "EndTime": 5897000, "VoiceStart": 5897000, "VoiceEnd": 5897000, "Speaker": "Speaker 1", "text": "Vy to tam nesmíte dávat. Vy to tam nesmíte strkat, jo? <PERSON>y ne podle EU, GDPR, nemůžete tady vzít Ravida Strejce a nemůžete jako jít na, já nevím, na justici nebo kde je u mě nejvíc informací. <PERSON>, mo<PERSON> rod<PERSON><PERSON> a tak, z<PERSON><PERSON><PERSON><PERSON><PERSON> to a dávat to do AI, to nem<PERSON>ž<PERSON> dělat, ale jít na Gemini a říct, kdo je <PERSON>, ud<PERSON><PERSON>j deep research, tak to můžete."}, {"StartTime": 5919000, "EndTime": 5919000, "VoiceStart": 5919000, "VoiceEnd": 5919000, "Speaker": "Speaker 1", "text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, d<PERSON><PERSON><PERSON>."}, {"StartTime": 5921000, "EndTime": 5921000, "VoiceStart": 5921000, "VoiceEnd": 5921000, "Speaker": "Speaker 1", "text": "<PERSON>, to a teď jsem chtěl je<PERSON> něco, teď už nevím, co jsem chtěl říct o tom předtím."}, {"StartTime": 5928000, "EndTime": 5928000, "VoiceStart": 5928000, "VoiceEnd": 5928000, "Speaker": "Speaker 1", "text": "<PERSON>, že s tím, že produktem jste vy ve smyslu jako, plat<PERSON> to i u tý AI, plat<PERSON> to v<PERSON><PERSON>. <PERSON>, j<PERSON>, jest<PERSON> jste hráli Angry Birdy, byla taková ta hra, jak se tam posílali ty ptáci na telefonu, eh, jak se tam stř<PERSON>leli jakoby z praku a ta hra o tu, ta o vás říkala jako absolutně všechno z toho telefonu, co tam našla, jo? Byla sice hezky zadarmo, ale obzvlášť u těch her na telefonech tohle platí dvojn<PERSON>, řekněme, ale u AI jste v případě Open AI, jak jste koukali, jak jsem tam měl tu srovnávací tabulku, tak, eh, tam je řečeno, že oni na těch datech trénujou a vy ty, vy ty data produkujete. Tí<PERSON> r<PERSON>, že vy se zeptáte AI, kolik je jedna a jedna? Vona vám řekne osm, jo? A vy řeknete, ale to je kravina, je to dva, tak tím stej<PERSON> v podstatě její další verzi, ne tu současnou, ale pro tu firmu Open AI jste její další verzi vylepšili. Protože se to sbírá od kvanta lidí, tak oni ty odpovědi vyhodnotí, vezmou ty relevantní, samozřejmě, lidi se bavej o různejch věcech, ale vezmou ty relevantní, ty nej"}]}