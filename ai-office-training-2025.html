<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Využití AI v kancelářské praxi 2025</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/dashicons@0.9.0/css/dashicons.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --bg-primary: #0a0a0a;
            --bg-secondary: #1a1a1a;
            --bg-card: rgba(255, 255, 255, 0.03);
            --text-primary: #ffffff;
            --text-secondary: #888888;
            --accent-primary: #e31937;
            --accent-secondary: #4ade80;
            --accent-success: #4ade80;
            --accent-warning: #fbbf24;
            --border-color: rgba(255, 255, 255, 0.1);
            --blur-amount: 10px;
        }
        
        [data-theme="light"] {
            --bg-primary: #ffffff;
            --bg-secondary: #f8f8f8;
            --bg-card: rgba(0, 0, 0, 0.03);
            --text-primary: #0a0a0a;
            --text-secondary: #666666;
            --accent-primary: #e31937;
            --accent-secondary: #22c55e;
            --accent-success: #22c55e;
            --accent-warning: #f59e0b;
            --border-color: rgba(0, 0, 0, 0.1);
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            overflow-x: hidden;
            scroll-behavior: smooth;
            transition: background 0.3s ease, color 0.3s ease;
            background: radial-gradient(circle at center, var(--bg-secondary) 0%, var(--bg-primary) 100%);
        }
        
        /* Theme Toggle */
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 50px;
            padding: 10px 20px;
            cursor: pointer;
            backdrop-filter: blur(var(--blur-amount));
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .theme-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(227, 25, 55, 0.3);
        }
        
        /* Navigation */
        .nav-dots {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 999;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .nav-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--bg-card);
            border: 2px solid var(--border-color);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .nav-dot.active {
            background: var(--accent-primary);
            transform: scale(1.3);
        }
        
        /* Sections */
        .section {
            min-height: 100vh;
            padding: 80px 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        
        .section:nth-child(odd) {
            background: radial-gradient(circle at center, #1a1a1a 0%, #0a0a0a 100%);
        }
        
        .section:nth-child(even) {
            background: radial-gradient(circle at center, #0a0a0a 0%, #1a1a1a 100%);
        }
        
        [data-theme="light"] .section:nth-child(odd) {
            background: radial-gradient(circle at center, #ffffff 0%, #f8f8f8 100%);
        }
        
        [data-theme="light"] .section:nth-child(even) {
            background: radial-gradient(circle at center, #f8f8f8 0%, #ffffff 100%);
        }
        
        .container {
            max-width: 1200px;
            width: 100%;
            margin: 0 auto;
        }
        
        /* Typography */
        h1 {
            font-size: clamp(3rem, 6vw, 5rem);
            font-weight: 700;
            letter-spacing: -3px;
            margin-bottom: 30px;
            background: linear-gradient(45deg, var(--accent-primary), #fff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: pulse 2s ease-in-out infinite;
        }
        
        h2 {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 300;
            margin-bottom: 20px;
            color: var(--text-primary);
        }
        
        h3 {
            font-size: clamp(1.5rem, 3vw, 2rem);
            font-weight: 400;
            margin-bottom: 15px;
            color: var(--accent-primary);
        }
        
        /* Cards */
        .card {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(var(--blur-amount));
            transition: all 0.3s ease;
            animation: slideUp 0.6s ease-out;
            transform: translateY(0);
            opacity: 1;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(227, 25, 55, 0.2);
            transform: translateY(-5px) scale(1.02);
        }
        
        /* Grid Layouts */
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }
        
        .tips-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }
        
        /* Feature Cards */
        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            transform: translateY(0);
            opacity: 1;
            animation: scaleIn 0.5s ease-out forwards;
        }
        
        [data-theme="light"] .feature-card {
            background: rgba(0, 0, 0, 0.03);
            border: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        .feature-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, var(--accent-primary) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }
        
        .feature-card:hover::before {
            opacity: 0.15;
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: var(--accent-primary);
            display: inline-block;
            animation: iconPulse 2s ease-in-out infinite;
        }
        
        @keyframes iconPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        /* Tip Cards */
        .tip-card {
            background: rgba(255, 255, 255, 0.03);
            border-left: 4px solid var(--accent-primary);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        [data-theme="light"] .tip-card {
            background: rgba(0, 0, 0, 0.03);
        }
        
        .tip-card:hover {
            transform: translateX(5px);
            border-left-color: var(--accent-secondary);
        }
        
        .tip-number {
            display: inline-block;
            width: 35px;
            height: 35px;
            background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
            color: #fff;
            border-radius: 50%;
            text-align: center;
            line-height: 35px;
            font-weight: bold;
            margin-right: 15px;
            box-shadow: 0 4px 10px rgba(227, 25, 55, 0.3);
        }
        
        /* Code Blocks */
        .code-block {
            background: rgba(227, 25, 55, 0.1);
            border: 1px solid rgba(227, 25, 55, 0.3);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            font-family: 'SF Mono', 'Monaco', 'Courier New', monospace;
            overflow-x: auto;
            position: relative;
            backdrop-filter: blur(5px);
        }
        
        [data-theme="light"] .code-block {
            background: rgba(227, 25, 55, 0.05);
            border: 1px solid rgba(227, 25, 55, 0.2);
        }
        
        .code-block::before {
            content: 'PROMPT';
            position: absolute;
            top: 5px;
            right: 10px;
            font-size: 0.8rem;
            color: var(--text-secondary);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        /* Lists */
        ul {
            list-style: none;
            padding-left: 0;
        }
        
        ul li {
            position: relative;
            padding-left: 30px;
            margin-bottom: 10px;
        }
        
        ul li::before {
            content: '';
            position: absolute;
            left: 0;
            top: 8px;
            font-family: 'dashicons';
            color: var(--accent-primary);
        }
        
        /* Animations */
        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.9; transform: scale(1.05); }
        }
        
        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        /* Comparison Table */
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        [data-theme="light"] .comparison-table {
            background: rgba(0, 0, 0, 0.03);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .comparison-table th {
            background: rgba(227, 25, 55, 0.1);
            font-weight: 600;
            color: var(--accent-primary);
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.9rem;
        }
        
        .comparison-table tr:hover {
            background: rgba(227, 25, 55, 0.05);
            transition: background 0.3s ease;
        }
        
        /* Interactive Elements */
        .tab-container {
            margin: 30px 0;
        }
        
        .tab-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .tab-button {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: var(--text-primary);
            font-weight: 500;
            backdrop-filter: blur(10px);
        }
        
        [data-theme="light"] .tab-button {
            background: rgba(0, 0, 0, 0.03);
            border: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        .tab-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(227, 25, 55, 0.2);
        }
        
        .tab-button.active {
            background: var(--accent-primary);
            color: #fff;
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 20px rgba(227, 25, 55, 0.3);
        }
        
        .tab-content {
            display: none;
            animation: fadeIn 0.5s ease;
        }
        
        .tab-content.active {
            display: block;
        }
        
        /* Progress Indicator */
        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
            z-index: 1001;
            transition: width 0.3s ease;
            box-shadow: 0 2px 10px rgba(227, 25, 55, 0.5);
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .section {
                padding: 60px 20px;
            }
            
            .nav-dots {
                display: none;
            }
            
            .feature-grid,
            .tips-grid {
                grid-template-columns: 1fr;
            }
        }
        
        /* Special Effects */
        .glow-text {
            text-shadow: 0 0 20px rgba(227, 25, 55, 0.5), 0 0 40px rgba(227, 25, 55, 0.3);
            animation: glow 2s ease-in-out infinite alternate;
        }
        
        @keyframes glow {
            from { text-shadow: 0 0 20px rgba(227, 25, 55, 0.5), 0 0 40px rgba(227, 25, 55, 0.3); }
            to { text-shadow: 0 0 30px rgba(227, 25, 55, 0.7), 0 0 50px rgba(227, 25, 55, 0.5); }
        }
        
        .gradient-border {
            background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
            padding: 2px;
            border-radius: 20px;
            animation: gradientRotate 3s linear infinite;
        }
        
        @keyframes gradientRotate {
            0% { background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary)); }
            50% { background: linear-gradient(315deg, var(--accent-primary), var(--accent-secondary)); }
            100% { background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary)); }
        }
        
        .gradient-border-inner {
            background: var(--bg-primary);
            border-radius: 18px;
            padding: 30px;
        }
        /* Additional Tesla-style enhancements */
        .section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 50%, rgba(227, 25, 55, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 50%, rgba(74, 222, 128, 0.1) 0%, transparent 50%);
            pointer-events: none;
            opacity: 0.5;
        }
        
        /* Animated background particles */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .card h4 {
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .card ul li {
            line-height: 1.8;
        }
        
        /* Enhanced hover states */
        .nav-dot:hover {
            background: rgba(227, 25, 55, 0.3);
            transform: scale(1.2);
        }
        
        /* Price/value styling like Tesla */
        .stat-value {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(45deg, var(--accent-primary), #fff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        /* Loading animation */
        .feature-card:nth-child(1) { animation-delay: 0.1s; }
        .feature-card:nth-child(2) { animation-delay: 0.2s; }
        .feature-card:nth-child(3) { animation-delay: 0.3s; }
        .feature-card:nth-child(4) { animation-delay: 0.4s; }
    </style>
</head>
<body>
    <!-- Progress Bar -->
    <div class="progress-bar" id="progressBar"></div>
    
    <!-- Theme Toggle -->
    <button class="theme-toggle" onclick="toggleTheme()">
        <i class="fas fa-moon" id="themeIcon"></i>
        <span id="themeText">Dark</span>
    </button>
    
    <!-- Navigation Dots -->
    <div class="nav-dots">
        <div class="nav-dot active" onclick="scrollToSection(0)"></div>
        <div class="nav-dot" onclick="scrollToSection(1)"></div>
        <div class="nav-dot" onclick="scrollToSection(2)"></div>
        <div class="nav-dot" onclick="scrollToSection(3)"></div>
        <div class="nav-dot" onclick="scrollToSection(4)"></div>
        <div class="nav-dot" onclick="scrollToSection(5)"></div>
    </div>
    
    <!-- Section 1: Úvod -->
    <section class="section" id="section-0">
        <div class="container">
            <div style="text-align: center;">
                <h1 class="glow-text">Využití AI v kancelářské praxi 2025</h1>
                <p style="font-size: 1.5rem; color: var(--text-secondary); margin-bottom: 40px;">
                    Praktický průvodce pro efektivní využití umělé inteligence
                </p>
                <div class="feature-grid" style="max-width: 800px; margin: 0 auto;">
                    <div class="feature-card">
                        <i class="fas fa-brain feature-icon"></i>
                        <h3>4 Moduly</h3>
                        <p>Komplexní školení pokrývající všechny aspekty AI v kanceláři</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-clock feature-icon"></i>
                        <h3>90 minut</h3>
                        <p>Intenzivní praktické školení s okamžitě použitelnými tipy</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-rocket feature-icon"></i>
                        <h3>Rok 2025</h3>
                        <p>Nejnovější AI nástroje a techniky pro moderní kancelář</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-users feature-icon"></i>
                        <h3>Interaktivní</h3>
                        <p>Praktické ukázky a prostor pro vaše dotazy</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Section 2: Modul 1 - Psaní a překlad -->
    <section class="section" id="section-1">
        <div class="container">
            <h2><i class="dashicons dashicons-edit"></i> Modul 1: Psaní a překlad</h2>
            
            <div class="tab-container">
                <div class="tab-buttons">
                    <button class="tab-button active" onclick="showTab('writing', 0)">
                        <i class="fas fa-envelope"></i> E-maily
                    </button>
                    <button class="tab-button" onclick="showTab('writing', 1)">
                        <i class="fas fa-file-alt"></i> Dokumenty
                    </button>
                    <button class="tab-button" onclick="showTab('writing', 2)">
                        <i class="fas fa-language"></i> Překlady
                    </button>
                    <button class="tab-button" onclick="showTab('writing', 3)">
                        <i class="fas fa-lightbulb"></i> Tipy & Triky
                    </button>
                </div>
                
                <div class="tab-content active" id="writing-0">
                    <h3>E-mailová komunikace s AI</h3>
                    <div class="tips-grid">
                        <div class="tip-card">
                            <span class="tip-number">1</span>
                            <strong>Personalizace ve velkém měřítku</strong>
                            <p>Využijte AI pro generování personalizovaných pozdravů a doporučení produktů na základě dat příjemců.</p>
                            <div class="code-block">
                                "Napiš uvítací e-mail pro zákazníka [jméno], který právě zakoupil [produkt]. Zachovej přátelský ale profesionální tón."
                            </div>
                        </div>
                        
                        <div class="tip-card">
                            <span class="tip-number">2</span>
                            <strong>Kontextové generování</strong>
                            <p>Poskytněte AI relevantní kontext včetně předchozí komunikace pro koherentní odpovědi.</p>
                            <div class="code-block">
                                "Na základě této e-mailové konverzace [vložit kontext] napiš zdvořilou odpověď, která řeší zákazníkův problém s dodávkou."
                            </div>
                        </div>
                        
                        <div class="tip-card">
                            <span class="tip-number">3</span>
                            <strong>Automatizace častých dotazů</strong>
                            <p>Natrénujte AI rozpoznávat časté dotazy a připravte šablony odpovědí.</p>
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="writing-1">
                    <h3>Tvorba dokumentů a prezentací</h3>
                    <div class="feature-grid">
                        <div class="card">
                            <h4><i class="dashicons dashicons-media-document"></i> Strukturované dokumenty</h4>
                            <ul>
                                <li>Vytváření osnov a struktury dokumentů</li>
                                <li>Generování sekcí na základě klíčových bodů</li>
                                <li>Kontrola konzistence a logické návaznosti</li>
                            </ul>
                        </div>
                        <div class="card">
                            <h4><i class="dashicons dashicons-slides"></i> Prezentace</h4>
                            <ul>
                                <li>Tvorba poutavých titulků a podtitulků</li>
                                <li>Generování bodů pro jednotlivé slidy</li>
                                <li>Návrhy vizuálních prvků a grafů</li>
                            </ul>
                        </div>
                        <div class="card">
                            <h4><i class="dashicons dashicons-welcome-write-blog"></i> Školící materiály</h4>
                            <ul>
                                <li>Přizpůsobení obsahu různým úrovním znalostí</li>
                                <li>Vytváření praktických cvičení</li>
                                <li>Generování kontrolních otázek</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="writing-2">
                    <h3>Pokročilé překladatelské techniky</h3>
                    <div class="gradient-border">
                        <div class="gradient-border-inner">
                            <h4><i class="fas fa-magic"></i> Lepší než "přelož"</h4>
                            <p style="margin-bottom: 20px;">Místo prosté žádosti o překlad používejte tento přístup:</p>
                            <div class="code-block">
                                "Přepiš tento e-mail do [jazyka], zachovej stejný styl, registr a tón. Respektuj kulturní kontext cílového jazyka."
                            </div>
                            <div style="margin-top: 20px;">
                                <strong>Proč to funguje lépe?</strong>
                                <ul>
                                    <li>Zachovává původní záměr a emoční náboj</li>
                                    <li>Přizpůsobuje se kulturním rozdílům</li>
                                    <li>Vytváří přirozenější text v cílovém jazyce</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="writing-3">
                    <h3>Top tipy pro rok 2025</h3>
                    <div class="tips-grid">
                        <div class="card">
                            <h4><i class="fas fa-star"></i> Ukládejte oblíbené prompty</h4>
                            <p>Vytvořte si knihovnu osvědčených promptů pro různé situace.</p>
                        </div>
                        <div class="card">
                            <h4><i class="fas fa-user-tie"></i> "Jednej jako" technika</h4>
                            <div class="code-block">
                                "Jednej jako profesionální copywriter. Napiš [délka] [tón] dopis pro [příjemce] v [jazyce] o [tématu]."
                            </div>
                        </div>
                        <div class="card">
                            <h4><i class="fas fa-check-double"></i> Kontrola gramatiky a stylu</h4>
                            <div class="code-block">
                                "Zkontroluj a oprav pravopisné, gramatické nebo stylistické chyby v tomto textu. Řekni mi, co jsi změnil a proč."
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Section 3: Modul 2 - DeepResearch AI Agenti -->
    <section class="section" id="section-2">
        <div class="container">
            <h2><i class="dashicons dashicons-search"></i> Modul 2: DeepResearch - AI výzkumní agenti</h2>
            
            <div class="card" style="margin-bottom: 30px; background: rgba(227, 25, 55, 0.1); border-color: var(--accent-primary);">
                <h3><i class="fas fa-brain"></i> Revoluce ve vyhledávání informací</h3>
                <p style="font-size: 1.1rem;">DeepResearch agenti představují zcela novou úroveň AI asistentů, kteří dokážou provádět hloubkový výzkum po dobu 5-30 minut a vytvářet komplexní reporty s citacemi.</p>
            </div>
            
            <h3>Top DeepResearch agenti v roce 2025</h3>
            <div class="tab-container">
                <div class="tab-buttons">
                    <button class="tab-button active" onclick="showTab('deepresearch', 0)">
                        <i class="fas fa-robot"></i> OpenAI Deep Research
                    </button>
                    <button class="tab-button" onclick="showTab('deepresearch', 1)">
                        <i class="fas fa-globe"></i> Google Gemini Deep Research
                    </button>
                    <button class="tab-button" onclick="showTab('deepresearch', 2)">
                        <i class="fas fa-atom"></i> Claude Research
                    </button>
                    <button class="tab-button" onclick="showTab('deepresearch', 3)">
                        <i class="fas fa-code-branch"></i> Open Source alternativy
                    </button>
                </div>
                
                <div class="tab-content active" id="deepresearch-0">
                    <div class="gradient-border">
                        <div class="gradient-border-inner">
                            <h4><i class="fas fa-microscope"></i> OpenAI Deep Research</h4>
                            <p><strong>Spuštěno:</strong> Únor 2025 | <strong>Model:</strong> o3 reasoning model</p>
                            
                            <div class="feature-grid" style="margin-top: 20px;">
                                <div class="card">
                                    <h5>Klíčové vlastnosti</h5>
                                    <ul>
                                        <li>Autonomní vyhledávání po dobu 5-30 minut</li>
                                        <li>Vytváří dokumenty s citacemi až na desítky stran</li>
                                        <li>Přesnost 26,6% na Humanity's Last Exam (2x lepší než konkurence)</li>
                                        <li>67% úspěšnost na GAIA benchmark</li>
                                    </ul>
                                </div>
                                <div class="card">
                                    <h5>Limity využití</h5>
                                    <ul>
                                        <li><strong>Free:</strong> 5 úkolů/měsíc (lightweight verze)</li>
                                        <li><strong>Plus/Team:</strong> 10 úkolů + 15 lightweight/měsíc</li>
                                        <li><strong>Pro:</strong> 125 úkolů + 125 lightweight/měsíc</li>
                                        <li>Maximum 100 úkolů/měsíc pro placené uživatele</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="code-block" style="margin-top: 20px;">
                                "Proveď hloubkovou analýzu českého trhu s elektromobily včetně legislativy, dotací a predikce vývoje do roku 2030"
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="deepresearch-1">
                    <div class="gradient-border">
                        <div class="gradient-border-inner">
                            <h4><i class="fas fa-search-plus"></i> Google Gemini Deep Research</h4>
                            <p>Osobní výzkumný asistent integrovaný do Google ekosystému</p>
                            
                            <div class="feature-grid" style="margin-top: 20px;">
                                <div class="card">
                                    <h5>Výhody</h5>
                                    <ul>
                                        <li>Pokrývá všechna témata napříč doménami</li>
                                        <li>Asynchronní task manager s error recovery</li>
                                        <li>Přímá integrace s Google Workspace</li>
                                        <li>Využívá mnoho modelových volání během několika minut</li>
                                    </ul>
                                </div>
                                <div class="card">
                                    <h5>Speciální funkce</h5>
                                    <ul>
                                        <li>Graceful error recovery</li>
                                        <li>Multi-step reasoning</li>
                                        <li>Kontextové propojení s Gmail a Docs</li>
                                        <li>Automatické ukládání výzkumu do Drive</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="deepresearch-2">
                    <div class="gradient-border">
                        <div class="gradient-border-inner">
                            <h4><i class="fas fa-graduation-cap"></i> Claude Research (Anthropic)</h4>
                            <p>Nejrychlejší research agent s Google Workspace integrací</p>
                            
                            <div class="feature-grid" style="margin-top: 20px;">
                                <div class="card">
                                    <h5>Unikátní vlastnosti</h5>
                                    <ul>
                                        <li>Dramaticky rychlejší než konkurence (minuty vs. 30 min)</li>
                                        <li>Přístup k Gmail, Calendar a Google Docs</li>
                                        <li>Agentické vyhledávání s iterativním učením</li>
                                        <li>Inline citace pro ověření zdrojů</li>
                                    </ul>
                                </div>
                                <div class="card">
                                    <h5>Enterprise funkce</h5>
                                    <ul>
                                        <li>Permission-based a session-limited přístup</li>
                                        <li>RAG techniky pro Drive Catalog</li>
                                        <li>Admin kontrola pro Team/Enterprise plány</li>
                                        <li>Security-first přístup</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="deepresearch-3">
                    <div class="gradient-border">
                        <div class="gradient-border-inner">
                            <h4><i class="fas fa-code"></i> Open Source DeepResearch</h4>
                            <p>Komunitní alternativy pro lokální běh</p>
                            
                            <div class="feature-grid" style="margin-top: 20px;">
                                <div class="card">
                                    <h5>LangChain implementace</h5>
                                    <ul>
                                        <li>Supervisor-researcher architektura</li>
                                        <li>Podpora modelů: Anthropic, OpenAI, Gemini</li>
                                        <li>Lokální běh s vlastními modely</li>
                                        <li>Customizovatelný přístup</li>
                                    </ul>
                                </div>
                                <div class="card">
                                    <h5>HuggingFace iniciativa</h5>
                                    <ul>
                                        <li>Otevřený agentic framework</li>
                                        <li>Možnost běhu "at home"</li>
                                        <li>Integrace s oblíbenými modely</li>
                                        <li>Kompletně lokální přístup</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <h3 style="margin-top: 40px;"><i class="fas fa-filter"></i> Omezení vyhledávání na specifické domény</h3>
            <div class="card" style="background: rgba(74, 222, 128, 0.1); border-color: var(--accent-secondary);">
                <h4>Techniky pro filtrování zdrojů</h4>
                <div class="tips-grid">
                    <div class="tip-card">
                        <span class="tip-number">1</span>
                        <strong>Operátor site:</strong>
                        <p>Použití Google site: operátoru pro omezení na konkrétní doménu</p>
                        <div class="code-block">
                            "site:*.cz elektromobily dotace 2025"
                        </div>
                    </div>
                    <div class="tip-card">
                        <span class="tip-number">2</span>
                        <strong>Prompt engineering pro domény</strong>
                        <p>Explicitní instrukce v promptu pro omezení zdrojů</p>
                        <div class="code-block">
                            "Analyzuj pouze informace z českých webů (.cz domén) a oficiálních vládních stránek"
                        </div>
                    </div>
                    <div class="tip-card">
                        <span class="tip-number">3</span>
                        <strong>RAG s vlastními zdroji</strong>
                        <p>Vytvoření vlastní databáze důvěryhodných českých zdrojů</p>
                        <div class="code-block">
                            "Použij pouze tyto zdroje: vlada.cz, mvcr.cz, justice.cz, czso.cz"
                        </div>
                    </div>
                </div>
            </div>
            
            <h3 style="margin-top: 40px;">České specifika a Seznam.cz</h3>
            <div class="comparison-table">
                <table style="width: 100%;">
                    <thead>
                        <tr>
                            <th>Vyhledávač</th>
                            <th>Podíl na trhu ČR</th>
                            <th>Speciální funkce</th>
                            <th>Domény</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Google</strong></td>
                            <td>74%</td>
                            <td>Deep Research, site: operátor</td>
                            <td>Všechny .cz domény</td>
                        </tr>
                        <tr>
                            <td><strong>Seznam.cz</strong></td>
                            <td>26%</td>
                            <td>Lokální obsah, Firmy.cz</td>
                            <td>Seznam.cz, Email.cz, Post.cz, Stream.cz</td>
                        </tr>
                        <tr>
                            <td><strong>CZ.NIC</strong></td>
                            <td>-</td>
                            <td>Správa .cz domén, blokování dezinformací</td>
                            <td>Všechny .cz registrace</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <h3 style="margin-top: 40px;">Praktické příklady použití DeepResearch</h3>
            <div class="feature-grid">
                <div class="card">
                    <h4><i class="fas fa-briefcase"></i> Business analýza</h4>
                    <div class="code-block">
                        "Vytvoř komplexní analýzu konkurence na českém trhu s potravinářstvím, zaměř se pouze na firmy registrované v ČR s obratem nad 100 mil. Kč"
                    </div>
                </div>
                <div class="card">
                    <h4><i class="fas fa-gavel"></i> Právní research</h4>
                    <div class="code-block">
                        "Analyzuj změny v české legislativě týkající se GDPR za poslední rok, použij pouze oficiální zdroje: zakonyprolidi.cz, mvcr.cz"
                    </div>
                </div>
                <div class="card">
                    <h4><i class="fas fa-chart-line"></i> Tržní analýza</h4>
                    <div class="code-block">
                        "Proveď analýzu českého realitního trhu v Praze, použij data z czso.cz, sreality.cz a české národní banky"
                    </div>
                </div>
            </div>
            
            <div class="card" style="margin-top: 40px; text-align: center;">
                <h3><i class="fas fa-lightbulb"></i> Tip pro české uživatele</h3>
                <p style="font-size: 1.1rem; margin-top: 15px;">
                    DeepResearch agenti zatím neumí nativně filtrovat pouze české zdroje, ale pomocí správného promptingu a operátoru site: 
                    můžete výsledky efektivně omezit na důvěryhodné české weby.
                </p>
                <p style="margin-top: 20px;">
                    <strong>Projekt OpenEuroLLM</strong> vedený Univerzitou Karlovou vytváří evropskou alternativu s podporou češtiny za 34 milionů EUR.
                </p>
            </div>
        </div>
    </section>
    
    <!-- Section 4: Modul 3 - Efektivní zadávání promptů -->
    <section class="section" id="section-3">
        <div class="container">
            <h2><i class="dashicons dashicons-format-chat"></i> Modul 3: Zásady efektivního zadávání promptů</h2>
            
            <div class="card" style="margin-bottom: 30px; text-align: center;">
                <h3>Klíč k úspěchu s AI = Kvalitní prompty</h3>
                <p style="font-size: 1.2rem;">Jasné, detailní a stručné prompty výrazně ovlivňují kvalitu odpovědí AI modelu.</p>
            </div>
            
            <h3>Který model AI na co? (2025)</h3>
            <div class="tab-container">
                <div class="tab-buttons">
                    <button class="tab-button active" onclick="showTab('models', 0)">
                        <i class="fas fa-bolt"></i> Rychlé úlohy
                    </button>
                    <button class="tab-button" onclick="showTab('models', 1)">
                        <i class="fas fa-brain"></i> Komplexní analýzy
                    </button>
                    <button class="tab-button" onclick="showTab('models', 2)">
                        <i class="fas fa-code"></i> Programování
                    </button>
                    <button class="tab-button" onclick="showTab('models', 3)">
                        <i class="fas fa-images"></i> Kreativní práce
                    </button>
                </div>
                
                <div class="tab-content active" id="models-0">
                    <div class="feature-grid">
                        <div class="card">
                            <h4><i class="fas fa-zap"></i> Claude Sonnet 4</h4>
                            <ul>
                                <li>Rychlé odpovědi na jednoduché dotazy</li>
                                <li>Sumarizace krátkých textů</li>
                                <li>Základní překlady</li>
                            </ul>
                        </div>
                        <div class="card">
                            <h4><i class="fas fa-rocket"></i> GPT-4o Mini</h4>
                            <ul>
                                <li>Rychlé generování e-mailů</li>
                                <li>Jednoduchá analýza dat</li>
                                <li>Základní kreativní psaní</li>
                            </ul>
                        </div>
                        <div class="card">
                            <h4><i class="fas fa-star"></i> Gemini 2.5 Flash</h4>
                            <ul>
                                <li>Ultra-rychlé zpracování</li>
                                <li>Velký kontext (1M tokenů)</li>
                                <li>Ideální pro rychlé iterace</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="models-1">
                    <div class="feature-grid">
                        <div class="card">
                            <h4><i class="fas fa-chess"></i> Claude Opus 4</h4>
                            <ul>
                                <li>Komplexní business analýzy</li>
                                <li>Strategické plánování</li>
                                <li>Hluboké porozumění kontextu</li>
                            </ul>
                        </div>
                        <div class="card">
                            <h4><i class="fas fa-microscope"></i> GPT-4o</h4>
                            <ul>
                                <li>Vědecké a technické analýzy</li>
                                <li>Multimodální zpracování</li>
                                <li>Komplexní reasoning</li>
                            </ul>
                        </div>
                        <div class="card">
                            <h4><i class="fas fa-project-diagram"></i> Gemini 2.5 Pro</h4>
                            <ul>
                                <li>Architektonická rozhodnutí</li>
                                <li>Thinking mode pro hluboké analýzy</li>
                                <li>1M tokenů kontextu</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="models-2">
                    <div class="feature-grid">
                        <div class="card">
                            <h4><i class="fas fa-terminal"></i> Claude Code</h4>
                            <ul>
                                <li>CLI-based coding agent</li>
                                <li>Autonomní generování kódu</li>
                                <li>Nejlepší pro terminálové workflow</li>
                            </ul>
                        </div>
                        <div class="card">
                            <h4><i class="fas fa-robot"></i> Cline</h4>
                            <ul>
                                <li>Open-source AI coding assistant</li>
                                <li>Plan/Act módy</li>
                                <li>Real-time debugging</li>
                            </ul>
                        </div>
                        <div class="card">
                            <h4><i class="fas fa-file-code"></i> GitHub Copilot</h4>
                            <ul>
                                <li>In-editor asistent</li>
                                <li>Široká podpora platforem</li>
                                <li>Multi-agent orchestrace</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="models-3">
                    <div class="feature-grid">
                        <div class="card">
                            <h4><i class="fas fa-palette"></i> DALL-E 3</h4>
                            <ul>
                                <li>Generování obrázků</li>
                                <li>Konzistentní styl</li>
                                <li>Editace a variace</li>
                            </ul>
                        </div>
                        <div class="card">
                            <h4><i class="fas fa-video"></i> Midjourney v6</h4>
                            <ul>
                                <li>Fotorealistické obrazy</li>
                                <li>Umělecké styly</li>
                                <li>Vysoké rozlišení</li>
                            </ul>
                        </div>
                        <div class="card">
                            <h4><i class="fas fa-music"></i> Suno AI</h4>
                            <ul>
                                <li>Generování hudby</li>
                                <li>Vlastní texty</li>
                                <li>Různé žánry</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <h3 style="margin-top: 40px;">Techniky psaní efektivních promptů</h3>
            <div class="gradient-border">
                <div class="gradient-border-inner">
                    <h4><i class="fas fa-layer-group"></i> Struktura dokonalého promptu</h4>
                    <div class="tips-grid">
                        <div class="tip-card">
                            <strong>1. Kontext</strong>
                            <p>Poskytněte relevantní pozadí</p>
                            <div class="code-block">
                                "Jsem manažer prodeje v technologické firmě..."
                            </div>
                        </div>
                        <div class="tip-card">
                            <strong>2. Úkol</strong>
                            <p>Jasně definujte, co chcete</p>
                            <div class="code-block">
                                "Potřebuji vytvořit týdenní report prodejních výsledků..."
                            </div>
                        </div>
                        <div class="tip-card">
                            <strong>3. Formát</strong>
                            <p>Specifikujte požadovaný výstup</p>
                            <div class="code-block">
                                "Formátuj jako strukturovaný seznam s hlavními body..."
                            </div>
                        </div>
                        <div class="tip-card">
                            <strong>4. Omezení</strong>
                            <p>Uveďte limity nebo požadavky</p>
                            <div class="code-block">
                                "Maximálně 500 slov, profesionální tón..."
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Section 5: Modul 4 - AI v Microsoft Office -->
    <section class="section" id="section-4">
        <div class="container">
            <h2><i class="dashicons dashicons-desktop"></i> Modul 4: Využití AI v Microsoft Office</h2>
            
            <div class="card" style="margin-bottom: 30px; background: rgba(255, 0, 110, 0.1); border-color: var(--accent-secondary);">
                <h3><i class="fas fa-exclamation-triangle"></i> Důležité srovnání</h3>
                <p style="font-size: 1.1rem;">Microsoft Copilot interface je bohužel výrazně horší než například <strong>Claude Code</strong> nebo <strong>Cline</strong>, které nabízejí intuitivnější ovládání a lepší výsledky.</p>
            </div>
            
            <h3>Automatizace v Word a Excel</h3>
            <div class="feature-grid">
                <div class="card">
                    <h4><i class="fas fa-file-word"></i> Word + AI</h4>
                    <ul>
                        <li>Generování dokumentů z šablon</li>
                        <li>Automatické shrnutí dlouhých textů</li>
                        <li>Kontrola konzistence dokumentu</li>
                        <li>Návrhy na vylepšení stylu</li>
                    </ul>
                    <div class="code-block" style="margin-top: 15px;">
                        "Analyzuj tento dokument a navrhni vylepšení struktury pro lepší čitelnost"
                    </div>
                </div>
                
                <div class="card">
                    <h4><i class="fas fa-file-excel"></i> Excel + AI</h4>
                    <ul>
                        <li>Analýza dat a trendy</li>
                        <li>Automatické vytváření vzorců</li>
                        <li>Prediktivní modelování</li>
                        <li>Vizualizace dat</li>
                    </ul>
                    <div class="code-block" style="margin-top: 15px;">
                        "Vytvoř pivot tabulku z těchto dat a identifikuj 3 hlavní trendy"
                    </div>
                </div>
                
                <div class="card">
                    <h4><i class="fas fa-file-powerpoint"></i> PowerPoint + AI</h4>
                    <ul>
                        <li>Generování slidů z osnovy</li>
                        <li>Návrhy designu a layoutu</li>
                        <li>Vytváření speaker notes</li>
                        <li>Optimalizace pro různá publika</li>
                    </ul>
                    <div class="code-block" style="margin-top: 15px;">
                        "Převeď tuto technickou zprávu na prezentaci pro management"
                    </div>
                </div>
            </div>
            
            <h3 style="margin-top: 40px;"><i class="fas fa-robot"></i> AI Agenti pro kancelář</h3>
            <div class="gradient-border">
                <div class="gradient-border-inner">
                    <h4>Praktické využití AI agentů v roce 2025</h4>
                    <div class="tips-grid">
                        <div class="card">
                            <h4><i class="fas fa-book"></i> Knihovník Agent</h4>
                            <p>Automaticky organizuje a katalogizuje dokumenty, vytváří metadata a doporučuje relevantní zdroje.</p>
                            <ul>
                                <li>Automatické tagování dokumentů</li>
                                <li>Vytváření souvisejících odkazů</li>
                                <li>Inteligentní vyhledávání</li>
                            </ul>
                        </div>
                        
                        <div class="card">
                            <h4><i class="fas fa-file-contract"></i> Právní Asistent</h4>
                            <p>Specializovaný agent pro práci s dodatky smluv a právními dokumenty.</p>
                            <ul>
                                <li>Analýza smluvních podmínek</li>
                                <li>Návrhy dodatků</li>
                                <li>Kontrola compliance</li>
                            </ul>
                        </div>
                        
                        <div class="card">
                            <h4><i class="fas fa-tasks"></i> Workflow Automatizátor</h4>
                            <p>Vytváří a optimalizuje pracovní postupy napříč aplikacemi.</p>
                            <ul>
                                <li>Automatizace rutinních úkolů</li>
                                <li>Integrace mezi aplikacemi</li>
                                <li>Sledování produktivity</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <h3 style="margin-top: 40px;">Alternativy k Microsoft Copilot</h3>
            <div class="comparison-table">
                <table style="width: 100%;">
                    <thead>
                        <tr>
                            <th>Nástroj</th>
                            <th>Výhody</th>
                            <th>Integrace</th>
                            <th>Cena</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Claude Code</strong></td>
                            <td>Lepší porozumění kontextu, CLI interface</td>
                            <td>Terminál, VS Code</td>
                            <td>Flexibilní</td>
                        </tr>
                        <tr>
                            <td><strong>Cline</strong></td>
                            <td>Open-source, Plan/Act módy</td>
                            <td>VS Code</td>
                            <td>Zdarma</td>
                        </tr>
                        <tr>
                            <td><strong>ChatGPT Plus</strong></td>
                            <td>Plugins, webové rozhraní</td>
                            <td>Web, API</td>
                            <td>$20/měsíc</td>
                        </tr>
                        <tr>
                            <td><strong>Gemini Workspace</strong></td>
                            <td>Nativní Google integrace</td>
                            <td>Google Suite</td>
                            <td>Součást Workspace</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </section>
    
    <!-- Section 6: Shrnutí a Q&A -->
    <section class="section" id="section-5">
        <div class="container">
            <h2><i class="dashicons dashicons-groups"></i> Shrnutí a diskuze</h2>
            
            <div class="card" style="text-align: center; margin-bottom: 40px;">
                <h3>Klíčové poznatky pro rok 2025</h3>
                <div class="feature-grid" style="margin-top: 30px;">
                    <div class="feature-card">
                        <i class="fas fa-key feature-icon"></i>
                        <h4>Kvalita promptů</h4>
                        <p>Jasné a specifické prompty = lepší výsledky</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-shield-alt feature-icon"></i>
                        <h4>Ověřování informací</h4>
                        <p>Vždy používejte více zdrojů a lidskou kontrolu</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-tools feature-icon"></i>
                        <h4>Správný nástroj</h4>
                        <p>Vyberte AI model podle typu úlohy</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-sync feature-icon"></i>
                        <h4>Kontinuální učení</h4>
                        <p>AI se rychle vyvíjí - zůstaňte v obraze</p>
                    </div>
                </div>
            </div>
            
            <h3>Praktické kroky k implementaci</h3>
            <div class="tips-grid">
                <div class="tip-card">
                    <span class="tip-number">1</span>
                    <strong>Začněte malými kroky</strong>
                    <p>Vyberte jednu oblast (např. e-maily) a experimentujte s AI nástroji.</p>
                </div>
                <div class="tip-card">
                    <span class="tip-number">2</span>
                    <strong>Vytvořte si knihovnu promptů</strong>
                    <p>Dokumentujte osvědčené prompty pro různé situace.</p>
                </div>
                <div class="tip-card">
                    <span class="tip-number">3</span>
                    <strong>Školte tým</strong>
                    <p>Sdílejte znalosti a nejlepší postupy s kolegy.</p>
                </div>
                <div class="tip-card">
                    <span class="tip-number">4</span>
                    <strong>Měřte efektivitu</strong>
                    <p>Sledujte časové úspory a kvalitu výstupů.</p>
                </div>
                <div class="tip-card">
                    <span class="tip-number">5</span>
                    <strong>Buďte kritičtí</strong>
                    <p>AI je nástroj, ne náhrada za lidský úsudek.</p>
                </div>
                <div class="tip-card">
                    <span class="tip-number">6</span>
                    <strong>Sledujte novinky</strong>
                    <p>Přihlaste se k odběru AI newsletterů a blogů.</p>
                </div>
            </div>
            
            <div class="card" style="margin-top: 40px; text-align: center; background: var(--bg-secondary);">
                <h3><i class="fas fa-question-circle"></i> Prostor pro vaše dotazy</h3>
                <p style="font-size: 1.2rem; margin-top: 20px;">Máte otázky k některému z probíraných témat?</p>
                <p style="margin-top: 30px;">
                    <i class="fas fa-envelope"></i> Kontakt: <strong><EMAIL></strong><br>
                    <i class="fas fa-globe"></i> Další zdroje: <strong>www.ai-vzdelavani.cz</strong>
                </p>
            </div>
            
            <div style="text-align: center; margin-top: 50px;">
                <h3>Děkuji za pozornost!</h3>
                <p style="font-size: 1.2rem; color: var(--text-secondary);">Hodně úspěchů při využívání AI ve vaší práci!</p>
            </div>
        </div>
    </section>
    
    <script>
        // Theme Toggle
        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.getElementById('themeIcon');
            const themeText = document.getElementById('themeText');
            
            if (body.getAttribute('data-theme') === 'light') {
                body.removeAttribute('data-theme');
                themeIcon.className = 'fas fa-moon';
                themeText.textContent = 'Dark';
            } else {
                body.setAttribute('data-theme', 'light');
                themeIcon.className = 'fas fa-sun';
                themeText.textContent = 'Light';
            }
        }
        
        // Tab Functionality
        function showTab(group, index) {
            const sectionMap = {
                'writing': 1,
                'deepresearch': 2,
                'models': 3
            };
            const sectionId = sectionMap[group] || 0;
            const buttons = document.querySelectorAll(`#section-${sectionId} .tab-button`);
            const contents = document.querySelectorAll(`[id^="${group}-"]`);
            
            buttons.forEach((btn, i) => {
                btn.classList.toggle('active', i === index);
            });
            
            contents.forEach((content, i) => {
                content.classList.toggle('active', i === index);
            });
        }
        
        // Smooth Scrolling
        function scrollToSection(index) {
            const section = document.getElementById(`section-${index}`);
            section.scrollIntoView({ behavior: 'smooth' });
        }
        
        // Update Navigation Dots and Progress Bar
        window.addEventListener('scroll', () => {
            const sections = document.querySelectorAll('.section');
            const navDots = document.querySelectorAll('.nav-dot');
            const progressBar = document.getElementById('progressBar');
            
            const scrollPosition = window.scrollY + window.innerHeight / 2;
            const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollPercentage = (window.scrollY / documentHeight) * 100;
            
            progressBar.style.width = scrollPercentage + '%';
            
            sections.forEach((section, index) => {
                const top = section.offsetTop;
                const bottom = top + section.offsetHeight;
                
                if (scrollPosition >= top && scrollPosition <= bottom) {
                    navDots.forEach(dot => dot.classList.remove('active'));
                    if (navDots[index]) {
                        navDots[index].classList.add('active');
                    }
                }
            });
        });
        
        // Intersection Observer for Animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animationPlayState = 'running';
                }
            });
        }, observerOptions);
        
        document.querySelectorAll('.card, .feature-card, .tip-card').forEach(el => {
            el.style.animationPlayState = 'paused';
            observer.observe(el);
        });
    </script>
</body>
</html>