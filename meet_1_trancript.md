{"segments": [{"StartTime": 0, "EndTime": 0, "VoiceStart": 0, "VoiceEnd": 0, "Speaker": "Automated Voice", "text": "You are currently the only person in this conference."}, {"StartTime": 134000, "EndTime": 134000, "VoiceStart": 134000, "VoiceEnd": 134000, "Speaker": "Speaker 1", "text": "Yes."}, {"StartTime": 149000, "EndTime": 149000, "VoiceStart": 149000, "VoiceEnd": 149000, "Speaker": "Speaker 2", "text": "Because it's actual."}, {"StartTime": 280000, "EndTime": 280000, "VoiceStart": 280000, "VoiceEnd": 280000, "Speaker": "Speaker 1", "text": "Všechno."}, {"StartTime": 281000, "EndTime": 281000, "VoiceStart": 281000, "VoiceEnd": 281000, "Speaker": "Speaker 2", "text": "všechno."}, {"StartTime": 281000, "EndTime": 281000, "VoiceStart": 281000, "VoiceEnd": 281000, "Speaker": "Speaker 1", "text": "<PERSON><PERSON><PERSON><PERSON>, v<PERSON>echny, já jsem tady."}, {"StartTime": 283000, "EndTime": 283000, "VoiceStart": 283000, "VoiceEnd": 283000, "Speaker": "Speaker 1", "text": "jsem tady, já jsem tady."}, {"StartTime": 285000, "EndTime": 285000, "VoiceStart": 285000, "VoiceEnd": 285000, "Speaker": "Speaker 1", "text": "jsem."}, {"StartTime": 286000, "EndTime": 286000, "VoiceStart": 286000, "VoiceEnd": 286000, "Speaker": "Speaker 3", "text": "No."}, {"StartTime": 287000, "EndTime": 287000, "VoiceStart": 287000, "VoiceEnd": 287000, "Speaker": "Speaker 4", "text": "Jo, slyším."}, {"StartTime": 290000, "EndTime": 290000, "VoiceStart": 290000, "VoiceEnd": 290000, "Speaker": "Speaker 1", "text": "To nemělo, já jsem šla na dovolenou v, v Pýnane."}, {"StartTime": 294000, "EndTime": 294000, "VoiceStart": 294000, "VoiceEnd": 294000, "Speaker": "Speaker 2", "text": "jsem tam."}, {"StartTime": 356000, "EndTime": 356000, "VoiceStart": 356000, "VoiceEnd": 356000, "Speaker": "Speaker 6", "text": "Dobrý den, slyšíme se?"}, {"StartTime": 357000, "EndTime": 357000, "VoiceStart": 357000, "VoiceEnd": 357000, "Speaker": "Speaker 7", "text": "Dobrý den."}, {"StartTime": 358000, "EndTime": 358000, "VoiceStart": 358000, "VoiceEnd": 358000, "Speaker": "Speaker 6", "text": "Dobrý den, tak nám se ještě."}, {"StartTime": 377000, "EndTime": 377000, "VoiceStart": 377000, "VoiceEnd": 377000, "Speaker": "Speaker 1", "text": "Musíte pak říct až budete všichni."}, {"StartTime": 381000, "EndTime": 381000, "VoiceStart": 381000, "VoiceEnd": 381000, "Speaker": "Speaker 2", "text": "Jo, jasně. Dobrý den."}, {"StartTime": 395000, "EndTime": 395000, "VoiceStart": 395000, "VoiceEnd": 395000, "Speaker": "Speaker 1", "text": "Tak, počkejte."}, {"StartTime": 411000, "EndTime": 411000, "VoiceStart": 411000, "VoiceEnd": 411000, "Speaker": "Speaker 1", "text": "zvuk."}, {"StartTime": 434000, "EndTime": 434000, "VoiceStart": 434000, "VoiceEnd": 434000, "Speaker": "Speaker 1", "text": "Co se týče? A?"}, {"StartTime": 436000, "EndTime": 436000, "VoiceStart": 436000, "VoiceEnd": 436000, "Speaker": "Speaker 3", "text": "No, nejnižší."}, {"StartTime": 437000, "EndTime": 437000, "VoiceStart": 437000, "VoiceEnd": 437000, "Speaker": "Speaker 4", "text": "dotaz."}, {"StartTime": 457000, "EndTime": 457000, "VoiceStart": 457000, "VoiceEnd": 457000, "Speaker": "Speaker 1", "text": "Jo."}, {"StartTime": 458000, "EndTime": 458000, "VoiceStart": 458000, "VoiceEnd": 458000, "Speaker": "Speaker 1", "text": "Jeden bod."}, {"StartTime": 459000, "EndTime": 459000, "VoiceStart": 459000, "VoiceEnd": 459000, "Speaker": "Speaker 1", "text": "dva."}, {"StartTime": 460000, "EndTime": 460000, "VoiceStart": 460000, "VoiceEnd": 460000, "Speaker": "Speaker 1", "text": "Tři."}, {"StartTime": 461000, "EndTime": 461000, "VoiceStart": 461000, "VoiceEnd": 461000, "Speaker": "Speaker 1", "text": "Čtyři."}, {"StartTime": 462000, "EndTime": 462000, "VoiceStart": 462000, "VoiceEnd": 462000, "Speaker": "Speaker 1", "text": "Jeden bod."}, {"StartTime": 463000, "EndTime": 463000, "VoiceStart": 463000, "VoiceEnd": 463000, "Speaker": "Speaker 1", "text": "pět."}, {"StartTime": 471000, "EndTime": 471000, "VoiceStart": 471000, "VoiceEnd": 471000, "Speaker": "Speaker 1", "text": "Tři."}, {"StartTime": 472000, "EndTime": 472000, "VoiceStart": 472000, "VoiceEnd": 472000, "Speaker": "Speaker 1", "text": "dva."}, {"StartTime": 473000, "EndTime": 473000, "VoiceStart": 473000, "VoiceEnd": 473000, "Speaker": "Speaker 1", "text": "Jedna."}, {"StartTime": 486000, "EndTime": 486000, "VoiceStart": 486000, "VoiceEnd": 486000, "Speaker": "Speaker 1", "text": "něco tady?"}, {"StartTime": 487000, "EndTime": 487000, "VoiceStart": 487000, "VoiceEnd": 487000, "Speaker": "Speaker 2", "text": "tady."}, {"StartTime": 488000, "EndTime": 488000, "VoiceStart": 488000, "VoiceEnd": 488000, "Speaker": "Speaker 1", "text": "tady."}, {"StartTime": 489000, "EndTime": 489000, "VoiceStart": 489000, "VoiceEnd": 489000, "Speaker": "Speaker 1", "text": "Jo, to se nedá zalít."}, {"StartTime": 494000, "EndTime": 494000, "VoiceStart": 494000, "VoiceEnd": 494000, "Speaker": "Speaker 3", "text": "tam ještě místo."}, {"StartTime": 496000, "EndTime": 496000, "VoiceStart": 496000, "VoiceEnd": 496000, "Speaker": "Speaker 1", "text": "Jo, jsem tam ještě."}, {"StartTime": 497000, "EndTime": 497000, "VoiceStart": 497000, "VoiceEnd": 497000, "Speaker": "Speaker 3", "text": "tam ještě místo?"}, {"StartTime": 498000, "EndTime": 498000, "VoiceStart": 498000, "VoiceEnd": 498000, "Speaker": "Speaker 1", "text": "Jo, tam je místo."}, {"StartTime": 499000, "EndTime": 499000, "VoiceStart": 499000, "VoiceEnd": 499000, "Speaker": "Speaker 3", "text": "Je tam místo?"}, {"StartTime": 500000, "EndTime": 500000, "VoiceStart": 500000, "VoiceEnd": 500000, "Speaker": "Speaker 4", "text": "Je tam místo."}, {"StartTime": 501000, "EndTime": 501000, "VoiceStart": 501000, "VoiceEnd": 501000, "Speaker": "Speaker 1", "text": "Ano, tam se tam ještě místo."}, {"StartTime": 506000, "EndTime": 506000, "VoiceStart": 506000, "VoiceEnd": 506000, "Speaker": "Speaker 5", "text": "seš dole?"}, {"StartTime": 507000, "EndTime": 507000, "VoiceStart": 507000, "VoiceEnd": 507000, "Speaker": "Speaker 6", "text": "dole."}, {"StartTime": 508000, "EndTime": 508000, "VoiceStart": 508000, "VoiceEnd": 508000, "Speaker": "Speaker 5", "text": "dole."}, {"StartTime": 509000, "EndTime": 509000, "VoiceStart": 509000, "VoiceEnd": 509000, "Speaker": "Speaker 6", "text": "dole."}, {"StartTime": 509000, "EndTime": 509000, "VoiceStart": 509000, "VoiceEnd": 509000, "Speaker": "Speaker 1", "text": "Dole."}, {"StartTime": 510000, "EndTime": 510000, "VoiceStart": 510000, "VoiceEnd": 510000, "Speaker": "Speaker 7", "text": "Tak se tam."}, {"StartTime": 511000, "EndTime": 511000, "VoiceStart": 511000, "VoiceEnd": 511000, "Speaker": "Speaker 1", "text": "Je to dobrý?"}, {"StartTime": 512000, "EndTime": 512000, "VoiceStart": 512000, "VoiceEnd": 512000, "Speaker": "Speaker 7", "text": "Je to dobrý?"}, {"StartTime": 513000, "EndTime": 513000, "VoiceStart": 513000, "VoiceEnd": 513000, "Speaker": "Speaker 1", "text": "asi, jo."}, {"StartTime": 515000, "EndTime": 515000, "VoiceStart": 515000, "VoiceEnd": 515000, "Speaker": "Speaker 7", "text": "Jo, asi jo."}, {"StartTime": 517000, "EndTime": 517000, "VoiceStart": 517000, "VoiceEnd": 517000, "Speaker": "Speaker 1", "text": "Už jsem zopakoval, že tu mám mísu."}, {"StartTime": 520000, "EndTime": 520000, "VoiceStart": 520000, "VoiceEnd": 520000, "Speaker": "Speaker 8", "text": "tam takovej."}, {"StartTime": 521000, "EndTime": 521000, "VoiceStart": 521000, "VoiceEnd": 521000, "Speaker": "Speaker 1", "text": "dobře."}, {"StartTime": 528000, "EndTime": 528000, "VoiceStart": 528000, "VoiceEnd": 528000, "Speaker": "Speaker 1", "text": "Takže jsem obešel osoby, ty, který jsou sedící v kanceláři, tak už by měli připojený. Co jsou mimo na home officu, tak taky, jestli dobře počítám, tak si myslím, že to"}, {"StartTime": 540000, "EndTime": 540000, "VoiceStart": 540000, "VoiceEnd": 540000, "Speaker": "Speaker 1", "text": "bude docela v klidu."}, {"StartTime": 541000, "EndTime": 541000, "VoiceStart": 541000, "VoiceEnd": 541000, "Speaker": "Speaker 9", "text": "Tak super."}, {"StartTime": 542000, "EndTime": 542000, "VoiceStart": 542000, "VoiceEnd": 542000, "Speaker": "Speaker 1", "text": "Asi, asi teda, jo."}, {"StartTime": 544000, "EndTime": 544000, "VoiceStart": 544000, "VoiceEnd": 544000, "Speaker": "Speaker 1", "text": "Prosím?"}, {"StartTime": 545000, "EndTime": 545000, "VoiceStart": 545000, "VoiceEnd": 545000, "Speaker": "Speaker 2", "text": "Vypni si kameru."}, {"StartTime": 546000, "EndTime": 546000, "VoiceStart": 546000, "VoiceEnd": 546000, "Speaker": "Speaker 1", "text": "Jo, klidně, ty jo. Já si potom, já tady začnu prezentovat a"}, {"StartTime": 551000, "EndTime": 551000, "VoiceStart": 551000, "VoiceEnd": 551000, "Speaker": "Speaker 1", "text": "Tak."}, {"StartTime": 557000, "EndTime": 557000, "VoiceStart": 557000, "VoiceEnd": 557000, "Speaker": "Speaker 1", "text": "Já asi budu muset nejdřív začít dotazem, eh, jestli už všichni z vás, eh, používají AI, protože to je vždycky nejdůležitější dotaz, abych se dostal na nějakou úroveň těch znalostí lidí, pro který to školení má být určeno. A v podstatě, eh, moje zpráva potom nakonec je, eh, tu řeknu rovnou na začátek, eh, kdy ještě snad všichni dávají pořádně pozor. A ta je, že bez používání AI to v současnosti už moc nejde a ani to nepůjde. A jde hlavně o to, že, eh, AI není magická stříbrná kulka, kterou, eh, ke které přijdete a všechno naráz."}, {"StartTime": 600000, "EndTime": 600000, "VoiceStart": 600000, "VoiceEnd": 600000, "Speaker": "Speaker 1", "text": "funguje, ale je to normálně eh, je to schopnost a dovednost a musí a musí se trénovat úplně stejně, jako se trénuje jóga třeba. A člověk se v tom zlepšuje pouze tehdy, pokud to používá a čím intenzivněji tu věc člověk používá, tak tím je v ní lepší a tím lépe i zná limity. Já tady s váma eh, úplně od začátku projdu, co je to AI, jak s ním pracovat, eh, projdu ty vaše témata, ale ono je to dost závislé i na tom, že doufám, že vám to kolegyně sdělila, že, eh, ty modely AI, eh, když budu říkat model a AI, tak se to rovná to slovo. A model je jenom verze, řekněme. Tak ty modely AI ve free verzích, v těch neplacených, jsou, řekněme, téměř k ničemu. Jo, to je takový, dá se to obcházet, dá se to obcházet složitě. Ukážu vám i jeden nástroj, který vám potom doporučím, který umí dělat tu vaši finální, ten váš finální požadavek pro dnešek, který je zadarmo, ale bez placené AI to v podstatě nejde. V současnosti ten největší standard je chat GPT, to určitě znáte všichni, nebo nevím, jestli to tady máte zaplacený, ale minimálně těch 20 dolarů a pokud to platí firma, tak, eh, jako minimálně těch 20 dolarů u Antropiku, je to za 18 dolarů, to je konkurenční firma. Takže, eh, protože já nerad, já, já si jenom pustím prezentaci, protože já nerad jako říkám věci, který sám nepoužívám a v angličtině se tomu říká Eat your own dog food, tak tuhle prezentaci mi asi před hodinou celou udělala AI. A je to jeden ze způsobů, který si myslím, že nahradí excely, všechno teď přechází do programátorských a programovacích věcí a aniž byste to věděli, tak všichni z vás jsou teď programátoři úplně bez problémů, protože nejmodernější programovací jazyk je angličtina."}, {"StartTime": 720000, "EndTime": 720000, "VoiceStart": 720000, "VoiceEnd": 720000, "Speaker": "Speaker 1", "text": "na respektive i čeština. AI modely se můžete bavit normálně v češtině. Já jsem mu jenom řekl, co chci a on mi toto vytvořil. Na konci té hodiny a půl budete schopni všichni tohle dělat. Předpokládá to nějaké instalace do počítače a trošičku technických znalostí. K téhle celkové, k tomuto celkovému školení nebo k tomu školení dostanete veškeré materiály. Můžete mi do toho kdykoliv vstupovat kdokoliv z vás. Bude to samozřejmě zahrnuto potom v těch materiálech, protože nám to školení potom AI přepisuje, AI z toho dělá souhrn, AI z toho dělá jako celkovej zápis a materiály. Pověďte. Teď jsem někoho slyšel."}, {"StartTime": 763000, "EndTime": 763000, "VoiceStart": 763000, "VoiceEnd": 763000, "Speaker": "Speaker 2", "text": "Já tady asi možná přesouvám jenom věci na stole, abych se i kolegové, kteří sedí vedle mě mohli do toho jednoduše zapojit. A my teda jako používáme, my jsme certifikační organizace, která provádí jakousi kontrolní činnost pro představu audity, audity systémů managementu. Úplně pro jednoduchou představu je to, že děláme prostě audity podle systému ISO 9001, jo? Potom samozřejmě naše portfólio není jenom, není, bohudík, tak jako jednoduchý. Máme i jiný schémata, podle kterých děláme kontrolní činnost. Kontrolní činnost většinou u nějakejch třetích stran, zákazníků, kteří něco vyrábějí nebo něco provádějí, posíláme tam auditory. Auditoři nám dělají zprávy, my ty zprávy čteme, hodnotíme, vystavujeme certifikáty. Je tam jako tříletá nějaká validita těch certifikátů, takže následný dva roky se k nim vracíme a kontrolujeme, jestli ten systém řízení nebo systém výroby je furt stejnej, splňující požadavky. Takže prakticky jako my hodně pracujeme s textem, pracujeme s normama, musíme zpracovávat požadavky, musíme ty požadavky znát, musíme je chápat a zavádět si náš vlastně interní systém informační systém, kterej používá vlastně"}, {"StartTime": 840000, "EndTime": 840000, "VoiceStart": 840000, "VoiceEnd": 840000, "Speaker": "Speaker 1", "text": "jakýkoliv pracovník, jak interní, tak vlastně i externí, což je ten auditor, aby aby vlastně jakoby postupoval podle jasných a vlastně aktuálních interních postupů, který vycházejí z nějakejch požadavkových norem, jo? To je vlastně naše práce. Zaměstnanci ty my máme asi šest placenejch licencí, takže někteří z někteří z této skupiny jako mají full verzi Chat GPT. Používáme si myslím jakože osoby tady z kanceláře pracují jenom jakoby eh v tady v tý doméně. Nejsem si jistej, jestli používá někdo jako, někdo tady odsaď ještě nějak, nějakou jinou umělou inteligenci nebo prostě nějakej nějaký rozhraní s s jiným jakoby výrobcem týdle umělý inteligence. Spíš ne. A eh u nás je tady 15, to znamená, že asi nějakejch 70 %, 60, 70 % prostě má tu neplacenou verzi."}, {"StartTime": 897000, "EndTime": 897000, "VoiceStart": 897000, "VoiceEnd": 897000, "Speaker": "Speaker 1", "text": "Jo?"}, {"StartTime": 897000, "EndTime": 897000, "VoiceStart": 897000, "VoiceEnd": 897000, "Speaker": "Speaker 2", "text": "Uhm."}, {"StartTime": 898000, "EndTime": 898000, "VoiceStart": 898000, "VoiceEnd": 898000, "Speaker": "Speaker 1", "text": "což asi teda potom nějakým způsobem budeme muset napravit. Eh, ale prostě jsou tady někteří, kteří vůbec nedělali a někteří, kteří s tím dělají intenzivně. Je to taková jako členitá skupina."}, {"StartTime": 903000, "EndTime": 903000, "VoiceStart": 903000, "VoiceEnd": 903000, "Speaker": "Speaker 2", "text": "Mhm."}, {"StartTime": 903000, "EndTime": 903000, "VoiceStart": 903000, "VoiceEnd": 903000, "Speaker": "Speaker 2", "text": "Tak budeme stejně muset začít úplně u základu a oni pravděpodobně i ti, kteří s tím dělají intenzivně, řekněme, tak se třeba dozví něco nového."}, {"StartTime": 909000, "EndTime": 909000, "VoiceStart": 909000, "VoiceEnd": 909000, "Speaker": "Speaker 1", "text": "Hm."}, {"StartTime": 909000, "EndTime": 909000, "VoiceStart": 909000, "VoiceEnd": 909000, "Speaker": "Speaker 1", "text": "Ok."}, {"StartTime": 910000, "EndTime": 910000, "VoiceStart": 910000, "VoiceEnd": 910000, "Speaker": "Speaker 2", "text": "takže to to v současnosti, čemu říkáme AI a je opravdu všechno důležité, to co teď budu říkat, tak eh, to je jakýsi model, eh, vezmou se veškeré, a teď zdůrazňuju, veškeré lidské znalosti, co kdy lidstvo vyprodukovalo v textu, které jsou jenom trochu použitelné, protáhne se to mašinama, eh, kdy pro představu, teď se staví cluster, který stojí 10 bilionů korun. A myslím to vážně, 500 miliard dolarů. Jo, nad těma mašinama se to protáhne, z toho na konci vyleze jeden excelovej soubor, dokonce jeden excelovej sheet, akorát má asi 500 GB nebo terabyte. A to je mozek umělý inteligence. Teď hrozně důležitá informace."}, {"StartTime": 960000, "EndTime": 960000, "VoiceStart": 960000, "VoiceEnd": 960000, "Speaker": "Speaker 1", "text": "ten mozek umělý inteligence se už nedoučuje. To je častá představa a častá chyba v reprezentaci toho, proč mi AI takzvaně halucinuje, neříká mi to, co má. A já to ihned uvedu na krásném příkladu. Tuhle AI vám budu doporučovat od Googlu, Google AI Studio, je to AI zadarmo a máte tam nejchytřejší model na planetě, který se teď jmenuje 2,5 Pro, Gemini 2,5 Pro, zase to budete mít všechno v materiálech v odkazech. týhle prezentaci, abyste si to mohli proklikat, vyzkoušet. Toto je skutečně úplně zadarmo, stačí tam mít účet u Googlu. A tato, když se jí zeptám, eh, kdo je prezident, já jsem se zeptal, eh, vám to zvětším, protože předpokládám, že to. Zeptal jsem se, kdo je prezident USA? Tak ona mi odpověděla, že prezident je Joe Biden. A spousta lidí začne v tuto chvíli nadávat a začne zavrhovat umělou inteligenci, že je to nesmysl a že nic neví a že se tvrdí, jak je to chytré a přitom to neví. To je, to vychází právě z toho, že ta umělá inteligence se trénuje dlouho, trénuje se několik měsíců, někdy čtyři, někdy šest měsíců na všech těch mašinách a oni jí musí udělat něco, čemu se říká anglicky Cutoff. Je to datum, po které ona zná reálie, ona zná ty data z internetu. Respektive z knih, z internetu, ze všech zdrojů, Facebook se přiznal, že kradl zdroje, že vykrádal o jako o copyrightech se vedou docela dlouhé spory, ale ten mozek umělé inteligence v sobě prostě obsáhne data pouze k určitému datu, k datumu a víc toho neví. A proto mi tady ten nejchytřejší model na planetě odpověděl, že je to Joe Biden. To je pro vás hodně důležité, protože umělá inteligence pracuje s daty a jeden jedny data jsou v tom jejím mozku, v tom v těch vytrénovaných datech, říká se tomu potom váhy."}, {"StartTime": 1080000, "EndTime": 1080000, "VoiceStart": 1080000, "VoiceEnd": 1080000, "Speaker": "Speaker 1", "text": "eh další data vy dáváte do něčeho, co se jmenuje kontext. Eh, když si tady přijde přejedeme do Chat GPT, kdy vy máte tu verzi 4O a ani si nemůžete jinou vybrat, eh, když používáte ten neplacený, já si můžu potom vybírat různé modely, to znají ti, kteří eh mají placenou verzi a zjednodušeně vždycky chcete pracovat s nejchytřejším modelem, což je tady ten O3 popřípadě O4 Mini. Za chvíli se k tomu dostanu, co a jak, ale já musím říct ještě jednu důležitou věc."}, {"StartTime": 1112000, "EndTime": 1112000, "VoiceStart": 1112000, "VoiceEnd": 1112000, "Speaker": "Speaker 2", "text": "to o tom, když tam překlikáváte z jední tý jakoby obrazovky na druhou, tak ono to není. Teďka to je vidět dobře, ale takový bývaj."}, {"StartTime": 1124000, "EndTime": 1124000, "VoiceStart": 1124000, "VoiceEnd": 1124000, "Speaker": "Speaker 1", "text": "Jo jo jo jo jo jo jo, já se snažím to mít jako, vždycky vám to přiblížím, jak to půjde, jo. Takže takže teď jsme normálně v Chat GPT, eh a ti co ji mají neplacenou, tak toto nejde přepnout, jo, já to znám od svýho syna, ten to má taky neplacený. A tady eh toto klikátko nejde změnit. Ti, co to mají placené, aspoň za těch 20 dolarů si tady můžou vybírat. Eh, můžete vyzkoušet neplacený i Clo AI, zase bude to v materiálech a tam máte ty modely chytřejší, neplacené. a je to limitováno. Stejně tak jako Chat GPT, všechno neplacené je limitováno, dokonce i ty placený, těch 20 dolarů mají ty věci limitované, protože provoz té AI je náročný a drahý na těch mašinách zjednodušeně. Proto proto ty zadarmo modely se navíc vůbec nerovnají ani tomu, co já dostávám. Oni to všechno ořezávají, všechno dělají na jako aby aby ušetřili, tak oni umí ty modely zhloupnout, řekněme a lidi si potom lidi potom dostávají úplně jiné výsledky. Když se dva zeptají na to samé a jeden má placený a druhý má neplacený, tak ty modely říkají něco jiného. A teď toto do čeho"}, {"StartTime": 1200000, "EndTime": 1200000, "VoiceStart": 1200000, "VoiceEnd": 1200000, "Speaker": "Speaker 1", "text": "se píše tady tato věc. To je chatové okno a potom se tomu říká kontextové okno. Já mu řeknu ahoj a on mi odpoví. Jo? Eh, a tím začínáme pracovat na kontextu. Ta AI pracuje s kontextem a já jsem jí tady přidal to ahoj do její, úplně to nejzákladnější jsem přidal do jejího kontextu. A potom se tomu říká kontextové okno. Je to vlastně je to to, co ona si v jednu chvíli se mnou může vyměňovat za informace. A to je omezené a to je potřeba jako vědět a dbát na to. Čím víc jí zaplním informacema to kontextové okno, tak tím ona dělá větší chyby teoreticky. Protože je to na ní moc zjednodušeně. Eh, platí to, že když je něco na začátku toho kontextového okna, ona si bude pamatovat to moje ahoj. Potom když se spolu budeme dál dlouho bavit, tak ten prostředek bývá horší a nejlíp si pamatuje a nejlíp reaguje na to, co je na konci. A to je důležité, zvlášť pro váš případ, pokud tam vkládáte velké textové eh, soubory, respektive velká textová data. Čím víc textových dat dáte do chat GPT, tak někdy dokonce můžete narazit na to, že řekne, že už se to tam nevejde. Jo? Já jdu já můžu jít na Wikipedia Česká republika a vzít si tady ten úplně bez problémů, můžete normálně přijít, udělat Ctrl A, Ctrl C a potom Ctrl V sem a eh, napsat jí eh, dej mi souhrn tohoto tohoto článku v bodech, eh, co nejjednodušší. A vidíte, že mi napsal tohle. Protože už je to na něj moc. Jo? Když vezmu to samé a dám to tady do eh, Google AI Studia do modelu Gemini 2."}, {"StartTime": 1320000, "EndTime": 1320000, "VoiceStart": 1320000, "VoiceEnd": 1320000, "Speaker": "Speaker 1", "text": "pro, který má kontextové okno milion tokenů. Tím se nemusíte zabývat, token je čtyřpísmenné slovo, prostě ekvivalent čtyřpísmenného slova a je to pro představu jeden a všechny svazky Harryho Pottera a ještě půlka z nich. Takže to v jednu chvíli umí zpracovávat. Víc ne. A já mu tady pošlu tohle, to samé s tím samým promptem, to znamená s tím samým mým požadavkem, dej mi souhrn toho článku v bodech, co nejjednodušší. Dám run. Tak eh, on začne přemýšlet, protože je to přemýšlecí model, tak dřív ty modely, dřív ty AI začli odpovídat hned. Teď ty modely jsou mnohem chytřejší a začnou o tom přemýšlet, co o něm, co po něm chci a začne si dělat plán, začne si dělat souhrn a proto mu to trvá. To taky lidi většinou otravuje, že to těm modelům, čím je to chytřejší model, tím mu to trvá dýl. Pro představu, Open AI má O3 Pro, který já už teď nemám tady přístupný v klikání, ale za 200 dolarů měsíčně potom můžete mít O3 Pro a ten přemýšlí standardně 20 minut, ale jeho inteligence, to jsem chtěl za chvilku ukázat, je asi 139 bodů IQ. Pochopení textu asi 200. Jo, tady je potom výstup toho, že z takhle dlouhého textu mi Google AI Studios je schopno normálně se o něm se mnou pobavit. A teď lidi často si představují, že ta AI je něco jako Wikipedie. Že přijdu a na něco se jí zeptám., to je mylná a chybná představa. AI je robot. Je to robotický mozek, není to chatbot, představte si to skutečně jako mozek, který je schopný reagovat a je schopný se se mnou normálně bavit o těch věcech, co já mu řeknu. Já mu třeba tady řeknu: Mně se to nelíbí, mně se tento, já to zvětším zase, abyste to viděli."}, {"StartTime": 1440000, "EndTime": 1440000, "VoiceStart": 1440000, "VoiceEnd": 1440000, "Speaker": "Speaker 1", "text": "že on udělal ten souhrn."}, {"StartTime": 1443000, "EndTime": 1443000, "VoiceStart": 1443000, "VoiceEnd": 1443000, "Speaker": "Speaker 1", "text": "a udělal souhrn. Potřebuju oscrollovat tady dolů. Šup. Eh, já to ještě dám větší. Tak a já řeknu normálně lidsky, je to moc krátké., je to moc krátké., udělej více bodů a rozšiř informace."}, {"StartTime": 1464000, "EndTime": 1464000, "VoiceStart": 1464000, "VoiceEnd": 1464000, "Speaker": "Speaker 1", "text": "A teď se dostáváme k tomu absolutně nejdůležitějšímu. On pracuje pouze s těmi informacemi, jak jsme říkali, že tady buď na trích je natrénovaný a nebo prioritizuje, čili upřednostňuje ty informace, které vy mu dáte."}, {"StartTime": 1480000, "EndTime": 1480000, "VoiceStart": 1480000, "VoiceEnd": 1480000, "Speaker": "Speaker 1", "text": "Já jsem vám to chtěl tady ukázat potom, až tady ten dopřemýšlí, až to dořeknu, tak vy ho můžete přesvědčit, že existuje nová matematika, dát mu podklady pro to, jak jak vypadá nová matematika a on se podle ní začne chovat a začne podle ní počítat."}, {"StartTime": 1495000, "EndTime": 1495000, "VoiceStart": 1495000, "VoiceEnd": 1495000, "Speaker": "Speaker 1", "text": "Stejně tak vy, pokud máte nějaké materiály, pokud podle kterých děláte ty certifikace, tak je potřeba si je předpřipravit a eh samozřejmě můžete k tomu využít AI, to je jako, já už vůbec nic jiného nedělám. Já nepíšu e-maily, nedělám nic, zadávám všechno jenom robotům a oni to dělají všechno za mě. To se kam se za chvíli dostaneme."}, {"StartTime": 1519000, "EndTime": 1519000, "VoiceStart": 1519000, "VoiceEnd": 1519000, "Speaker": "Speaker 1", "text": "Ale eh to co je vlastně teď ten nejlepší skill je rychločtení, protože nějaká vizuální a rychlá kontrola, jo, hlavní město Praha přibližně 1,3 milionu. On si v takovýchto případech nevymýšlí, když má ty informace k dispozici, tak jak jsme mu je dali přímo z té Wikipedie."}, {"StartTime": 1540000, "EndTime": 1540000, "VoiceStart": 1540000, "VoiceEnd": 1540000, "Speaker": "Speaker 1", "text": "Ale tenhle robot, tenhle nejchytřejší model v tomhle Google AI studiu nemá přístup na internet. Jo? To proto tvrdil, že prezident je Joe Biden. Když já přijdu do Chat GPT a zeptám se ho tady, kdo je prezident, kdo je prezident USA. On má taky ten cut off."}, {"StartTime": 1560000, "EndTime": 1560000, "VoiceStart": 1560000, "VoiceEnd": 1560000, "Speaker": "Speaker 1", "text": "On je taky dotrenovanej do nějakého data, ale protože už funguje agenským způsobem, nejenom, že přemýšlí, ale i pošle svého agenta, pošle za sebe nástroj, na vyhledávání na webu. Vidíte, že tady si začal hledat na webu a našel si informace, aby se obohatil. On ví, že neví a proto hledá na internetu. Jo? A to je v podstatě, to, co ty modely teď dělají. Kam se to posunulo? Posunulo se to tam, že toto je jednoduché hledání, přičemž v chat GPT v tom placeném. Potom můžete mít deep research, což vy máte taky, o to jste měli zájem o hledání a ověřování informací. Deep research je najdi mi veškeré informace z českého internetu o Petru Pavlovi. Jo? A u těch deep researchu, to ještě na to dává na to pozor, na to jsem se párkrát nachytal. On se vás ptá, protože předpokládá, že vaše otázka na ten deep research není nikdy definovaná dokonale. Jo? On se vás ptá na otázku, eh, chci, chci veškeré informace, co najdeš. Stačí ho zahnat druhým. Nemusíte se bát překlepů, nemusíte, on rozumí i kombinaci jazyků, jo, to tam taky potom máte na to dotaz. Takže, eh, to je právě o tom používání. Když ji používáte, píšete jí překlepy a vidíte, že ona tomu stejně rozumí, tak se nemusíte jako zabývat takovejma drobnostma. A ten deep research, eh, trvá podle druhu AI u Chat GPT, je to třeba, já nevím, eh, 10 minut, 15 minut. U Clouda je to třeba, když mu řeknu to samý, řeknu mu tady, eh,"}, {"StartTime": 1680000, "EndTime": 1680000, "VoiceStart": 1680000, "VoiceEnd": 1680000, "Speaker": "Speaker 1", "text": "research."}, {"StartTime": 1682000, "EndTime": 1682000, "VoiceStart": 1682000, "VoiceEnd": 1682000, "Speaker": "Speaker 1", "text": "Zapnu a dám tady Opus. Tak on hledá třeba na 400 stránkách. Jo, 400 webovejch stránkách začne vyhledávat a začne o tom hledat informace a z toho mi nakonec udělá report."}, {"StartTime": 1698000, "EndTime": 1698000, "VoiceStart": 1698000, "VoiceEnd": 1698000, "Speaker": "Speaker 1", "text": "Na měsíc zdarma. Případně máte i u Googlu Gemini. Já mám všechny placený, ale Gemini máte možnost si u Googlu zase zapnout na měsíc zdarma úplně a porovnávat si ty nástroje. Zase je tady Deep Research, kterej můžete zapnout a zase oni to od sebe kopírujou, on se mě hned zeptá na otázku, jaké informace chci. Kloce neptá. Tady je potom vidět, že si dělá plán a začne normálně fungovat jako, jako člověk v podstatě. A toto se dá získat jenom praxí ten prompting jakoby, to, co mu říkám. Když, protože každé slovo je důležité a já když mu řeknu z českého internetu, tak může mít vliv, jestli řeknu z českého internetu a nebo řeknu z domény.cz. Český internet taky pro něj může znamenat, že jsou to domény com a je to psáno česky. Čím specifičtější a přesnější v tom zadání tomu robotovi jsem, tak tím lepší můžu očekávat výsledky. Stejně tak to platí o datech. Když mu dám nějaké PDF, návody a je to zahlceno nějakými marketingovými informacemi, tak potom mi může dávat špatné výsledky, protože jeho mozek, ten kontext, co jsem mu dal, je vlastně přetížený těma těma informacema, který jsou nerelevantní. Čili s AI je nejlepší se bavit co nejčistšíma informacema, stručnýma, čistýma, jo? Tady si už si našel 40 zdrojů a za chvíli jich tam bude 400, už jich má 72, už jich má 100. Všechny fungujou stejně, jak jsem říkal, takže zahájit výzkum ten"}, {"StartTime": 1800000, "EndTime": 1800000, "VoiceStart": 1800000, "VoiceEnd": 1800000, "Speaker": "Speaker 1", "text": "udělat stejnej plán. Z každého bohužel lezou různé výsledky. Jo a proto je potřeba to používat, otestovat si ideálně tyhle zadarmo, jak Gemini na měsíc zadarmo, tak Clouda, popřípadě zainvestovat na jeden měsíc těch 15 dolarů nebo kolik chtěj nebo 18, vyzkoušet si ty nástroje, s čím se i člověku pracuje nejlíp. Protože může to být odlišné, jo a je to skutečně o tom, když vy pracujete s daty, takže, eh, pro toho robota pro tu AI jsou data alfa a omega. A když jí dáte předpřipravené, čisté a kvalitní, tak potom na tom základě ona pracuje. Jo a žádné magické prompty, jak by vám možná říkali někteří jiní, eh, ona v tom moc magie není. Eh, je to, je to o stručnosti. Takže, eh, jak jsem tady řekl, najdi mi, najdi mi veškeré informace z českého internetu o Petru Pavlovi, tak je to ten můj požadavek je vlastně vyslovení pro robota toho, co po něm skutečně chci a nemusím se tím úplně sáhodlouze a složitě zabývat, protože jak jsme si tady ukazovali, tak já na to můžu reagovat a to je, zase, mnoho lidí si myslí, že ta první odpověď od AI je finální a tady tímhle jsem vám chtěl ukázat, kdy jsem mu nejdřív dal ty zdrojová data od České republiky a potom jsem si z nich nechal udělat stručný výpis a mně se nelíbil. To je, eh, skoro vždycky to tak je, že ta, to ten první nástřel není ideální, ale s AI se pracuje iterativně, jako to znamená, že mu říkám, co je špatně, co se mi nelíbí, co chci upravit, jak to chci vylepšit. A, eh, funguje to víc než dobře. Jo?"}, {"StartTime": 1920000, "EndTime": 1920000, "VoiceStart": 1920000, "VoiceEnd": 1920000, "Speaker": "Speaker 1", "text": "protože potom eh budeme si to ukazovat třeba na tom chat GPT, když ho máte, tak on je schopen tvořit i prezentace. Já můžu tady napsat vytvoř a a tam je potřeba občas znát magické slova, to je jedno, uvedu, vytvoř mi prezentaci o Petru Pavlovi. Pavlovi a použij Canvas, jo? To je to magické slovo Canvas, budete ho zase mít v těch materiálech. U Antropiku se to jmenuje Artifacts. A on udělá takovou magii. Já si použiju tady O4 mini model. Pozor na mini. Mini vždycky, buď je to flash, mini, jakmile je tam něco, co připomíná rychlost, tak ten model je sice rychlý, ale ne tak chytrý. Jo? Až bude O4 plný, tak bude chytřejší, ale o dost pomalejší. Takže si použijeme O4 mini a udělej to v HTML plus CSS. A teď on si pustí tady takovou zvláštní věc a začne psát kód. Proto jsem říkal, že ze všech z vás všech, jo, on si nejdřív udělá výzkum, což taky může a potom by měl použít eh takovou tuhle tu věc a začne psát HTML kód. Proto jsou z vás ze všech programátoři, protože on vytvoří HTML soubor, který můžete poslat kolegovi místo prezentace. Je to to, co tady mám puštěné já. To je normální HTML soubor, puštěný v prohlížeči. Jo? Takže eh normálně když ho kolegovi pošlete e-mailem a nebo ho potom pustíte v prohlížeči dvojklikem, tak je z toho takováto prezentace. A eh teď jsme v tom modulu psaní a překlad. Tam se bavíme o Promptech, což je to, co vy po té AI chcete. A tam"}, {"StartTime": 2040000, "EndTime": 2040000, "VoiceStart": 2040000, "VoiceEnd": 2040000, "Speaker": "Speaker 1", "text": "to se učí. Eh, kdyžtak děti s tím můžou pomoct na středních školách normálně. Je to standardní HTML kód, HTML prezentace, kterou potom můžete vzít a dát download a teď se mi to stáhlo a já to tady jedním klikem pustím. Petr Pavel, jo? Životopis, armáda, NATO, kampaň. A mám tady vytvořenou prezentaci. Je puštěná v prohlížeči. Stačí to takto nebo"}, {"StartTime": 2045000, "EndTime": 2045000, "VoiceStart": 2045000, "VoiceEnd": 2045000, "Speaker": "Speaker 2", "text": "No dobře."}, {"StartTime": 2045000, "EndTime": 2045000, "VoiceStart": 2045000, "VoiceEnd": 2045000, "Speaker": "Speaker 1", "text": "místo místo, aby to mělo tečka PPT, tak to má tečka HTML."}, {"StartTime": 2050000, "EndTime": 2050000, "VoiceStart": 2050000, "VoiceEnd": 2050000, "Speaker": "Speaker 2", "text": "Dobře, a to jenom prostě pošlu jako v příloze nebo prostě v nějakým e-mailem?"}, {"StartTime": 2053000, "EndTime": 2053000, "VoiceStart": 2053000, "VoiceEnd": 2053000, "Speaker": "Speaker 1", "text": "Ano, pošlete normálně v příloze, to pošlete, ano, to pošlete normálně v příloze a když na to člověk klikne, stejně tak jako já tady mám od vás struktura školení v docu, jako otvírám teď ten soubor, tak úplně stejným způsobem, když otevřu tečka HTML soubor, tak mám toto před sebou."}, {"StartTime": 2069000, "EndTime": 2069000, "VoiceStart": 2069000, "VoiceEnd": 2069000, "Speaker": "Speaker 2", "text": "Mhm."}, {"StartTime": 2070000, "EndTime": 2070000, "VoiceStart": 2070000, "VoiceEnd": 2070000, "Speaker": "Speaker 2", "text": "Dobře, tak to jsem ještě s tím neměla zkušenost, to vyzkouším. Děkuju."}, {"StartTime": 2074000, "EndTime": 2074000, "VoiceStart": 2074000, "VoiceEnd": 2074000, "Speaker": "Speaker 1", "text": "Jo, tam právě i tato a ono to může, toto je celé jeden HTML soubor úplně stejným způsobem vytvořený, jako jsem vám teď ukazoval. A eh, můžete si takhle dělat hodně hezké, ještě nahoře jezdí tady ta zelená žížalka, hodně hezké interaktivní blikající prezentace. A to není celé, jo? Vy potom to můžete vzít a tady ta je velmi jednoduchá, jo, tady to, co jsme si teď udělali, tady toto."}, {"StartTime": 2160000, "EndTime": 2160000, "VoiceStart": 2160000, "VoiceEnd": 2160000, "Speaker": "Speaker 1", "text": "No, kde to máme? Eh, tady je Gemini, tady, tady teď jsme hledali tady. Tady. Tak. To je dokonce on, eh, tady mám preview, jo? Tady je potom čudlík, nevím, jestli je to i v češtině potom a vidíte, že on mi to normálně pustí i v tom rozhraní toho chat GPT. A takhle to vypadá. A já mu teď řeknu, eh, je to nudné, jo? Tady mu můžu říct, je to nudné. Je to nudné. Udělej prezentaci hezčí, zveď písmo, dej tam víc informací, eh, udělej tam nějaké ikonky a ať se na to hezky dívá. Vidíte, že jsem vůbec netrápím nějakým jako magickým promptováním. A v podstatě se snažím říct svoji myšlenku. A já si potom, jo, a on to začne editovat. Začnu editovat ten soubor, to jsem říkal, že jsou vaši programátoři, protože on normálně zedituje ten kód a to se nakonec dostaneme, že on takhle umí pracovat i s s Excelama, dokonce je umí stahovat z internetu, vytvářet. A je to v podstatě pořád to samé, že tomu robotovi zadávám to, co po něm chci. To, co, eh, jak se mi to nelíbí, kde se mi to nelíbí. Eh, můžeme si nechat upravit jenom jednu stránku, jo? Aby aby bylo jasné, že když mu dávám specifické instrukce, tak to dělá. Takže já tady můžu dát preview, eh, tady dám allow selected, allow selected. A vidíte, že tam normálně přidal ikonky. Jo, můžu si to zase radši stáhnout, aby to bylo to, aby to bylo jasný. A teď, jo, život, vzdělání, dokonce to začalo jezdit, armáda, NATO. A takhle postupně si můžu, eh, s ním dělat až po takovýto typ prezentace. Každý, to je právě"}, {"StartTime": 2280000, "EndTime": 2280000, "VoiceStart": 2280000, "VoiceEnd": 2280000, "Speaker": "Speaker 1", "text": "potom používání a trik potom na to je, že jakmile dojdu do něčeho pěkného, co se mi začne líbit, tak se to dá normálně znovu použít. Já můžu jít k tomu souboru. tady vzít si ten HTML kód, jo, tady on potom vypadá takhle, když ho otevřete třeba i ve Wordu, tak je vám úplně jedno, co tam je napsané za písmenka. A když jste tam měli zmíněné překlady, tak já můžu toto vzít, udělat Ctrl C, jít do chat GPT. A pomocí Ctrl V mu to tam normálně, on to jde i připojit, jo, ten soubor. HTML normálně přečte, ale klidně i takhle. A normálně mu řeknu, já nevím, v jakých jazycích pracujete, přelož tuto prezentaci do mongolštiny. A on ji přeloží. Tom k těm překladům, tam to mám uvedené v té prezentaci mojí, kdy tady máme, kde pak ji mám. prezentace, jo, moment. Jsem si dal. Tak. K těm překladům, což tady máte zmíněné taky, že potřebujete, tak místo, místo, on to tady pěkně zmiňuje. Místo než přelož mu řeknete, přepiš tento email nebo text do jazyka, teď mongolština, čínština, japonština. Zachovej stejný styl, registr a tón, respektuj kulturní kontext cílového jazyka. S tímhle je potřeba si případně ještě trochu hrát. Je to takový nejbližší prompt, ta specifikace toho, co po něm chci. Při přelož, on bude dost strojový a nemusí zachytit"}, {"StartTime": 2400000, "EndTime": 2400000, "VoiceStart": 2400000, "VoiceEnd": 2400000, "Speaker": "Speaker 1", "text": "úplně veškeré nuance u toho u jazyku, jo. A nejlepší model na překlady, zase čím chytřejší model, tím lepší, ale u chat GPT je málo využívaný model, který se jmenuje 4 5, jo, a ten je úplně největší teď na světě, co vůbec je, je málo používaný a na překlady a na ty jazykové nuance a na vlastně na na přesnost překladů je teoreticky nejlepší, ale zase je potřeba z z každého si to vyzkoušet a jedni tvrdí, že je nejlepší O3, jedni tvrdí, že je to 45, jedni tvrdí, že je to tady od Googlu, takže Gemini je prostě potřeba s tím pracovat a člověk co co člověk to v kus prostě, to se říká jako velmi, velmi složitě a ještě pro různé typy někdy ten jeden model může být lepší na technické texty, některý může být lepší na eh, knihy, na jako normální beletrii. A proto je to to používání. Čím víc to budete používat, tím víc znáte ty limity a eh, to se v skutečně jinak než používáním naučit nedá. Eh, takže teď jsme si snad řekli, k promptům, ke kontextu a k tomu, co ta AI ví, eh, všechno. Tam záleží, jestli máte k tomu nějaké dotazy k tomuto psaní a překlad případně. Jo? Ona Gemini jinak existuje i v Google AI Studio, teda Google Google Docs. Záleží já bych se chtěl zeptat, jestli můžu? Povídejte. Já bych se chtěl zeptat jednak eh eh porovnání těch AI modelů, kterej je lepší, jestli Chat GPT nebo Gemini nebo od Microsoftu, z vaší vlastní zkušenosti. Jo, tak od Microsoftu Copilot je v podstatě nejhorší z těchhle největších."}, {"StartTime": 2520000, "EndTime": 2520000, "VoiceStart": 2520000, "VoiceEnd": 2520000, "Speaker": "Speaker 1", "text": "Já na to používám vždycky tuhle tabulku, je to nějakej nezávislej IQ test. oni tam pořád právě ještě navíc je potřeba vědět, že extrémně rychle se to mění., tady je vidět, jak byly modely chytrý před rokem. 3 ten už ho vyhodili. před rokem to mělo IQ 96, teď to má IQ 133, v některých testech 139. Jo? Eh, nejchytřejší je asi O3. určitě je to O3 pro pro extrém. Ten je schopnej udělat byznys analýzu pro velkou firmu. Já s ním teď třeba budu dělat Woody Sport. Jako kompletní celkovou byznys analýzu založenou jenom na datech, jenom na analytickejch věcech, je schopnej predikovat ceny akcií, teď s tím dělají, investujou s tím do Bitcoinů, jenom že si hledá informace, dělá nějaký predikční věci, klidně s tím dělají high frequency trading. Takže O3 Pro, on je, on je i nejdražší. Musíte ho mít za 200 dolarů. Já jsem to teď před dvěma dnema skončil předplatný 200 dolarů měsíčně u toho O3 pro, to úplně za to nestojí, ten model O3 stačí. Eh, 2,5 Gemini 2,5, tady to na co jsme se dívali je řekněme o procento horší, o procento lepší, to to záleží, je to zase to nejlepší, co existuje. Google má spoustu dat, skvělý čipy, skvělý lidi., lidi mu nevěřili. Gemini 2,5 Pro je teď hodně dobrá, ale já osobně používám Claude, protože on má takzvanej flat, on se dá koupit za 100 dolarů a program a mám jakoby nekonečný použití. Ale vždycky je to ten top model od tý firmy. Jo, je to 2,5 Pro, u těch u Uchat GPT je to O3, určitě ne O4 Mini, je to ten O3 a u U Anpic, já používám"}, {"StartTime": 2640000, "EndTime": 2640000, "VoiceStart": 2640000, "VoiceEnd": 2640000, "Speaker": "Speaker 1", "text": "opuse plot opus, ale to jsou nuance. Je ten top model těch firem, ty všechny tři, ono je to tady vidět i v tom IQ testu, jsou téměř jako na tom stejně, jo. Oni třeba Antropik je nejlepší na programování, takže proto my ho ve ve firmě ho mají úplně všichni programátoři a my to jedeme trošku jinak. Chat GPT je nejobecnější, je tam nejvíc věcí. Chtěl jsem vám třeba i ukázat, co se tam dá zapnout u nich. Je tam jedna věc, pro ty, co to moc nepoužívají, tak já můžu říct tady eh, menu nebo eh, mám rád aerobní cviče, cvičení. Ulož si to o mě do paměti. Tohle je jediný způsob, eh, že jak já můžu říct, eh, já chci Zumbu. Mám rád Zumbu. Jak můžu přenášet informace mezi chatama. Je to tady potom nastavitelné, že tady dám settings a tady dám personalization a tady je memory. A tady česky to tam bude normálně přizpůsobení a paměť a tady je já si, eh, já si můžu, on si záhadným způsobem občas řekne, co si o mě uloží, já si to tady můžu, eh, modifikovat, respektive vyhazovat. A když mu řeknu, aby si o mě uložil nějakou paměť, tak si ji uloží do paměti a je to taková malá informace, aby si právě nezaplácel kontext, jako třeba, že rád piju kafe a, já nevím, že mám rád nějaký typ pohádek. A, eh, může se to i vypnout, ale to je jediný způsob, jak přenesu z jednoho chatu do druhého, bez toho, abych se něčeho dotýkal informace. Jinak to musím normálně kopírovat a musím si tvořit, eh, prostě nějaký svůj"}, {"StartTime": 2760000, "EndTime": 2760000, "VoiceStart": 2760000, "VoiceEnd": 2760000, "Speaker": "Speaker 1", "text": "promt a nějaký svůj obsah. A ta inteligence těch modelů, já vám dneska řeknu, že je jeden nejinteligentnější a teď vyjde Grok 3 a půl pravděpodobně, nebo oni chtějí, ho chtějí nazvat Grok 4. To máte v Twitteru taky zdarma, pokud to máte jako máte zas placenej Twitter, že jo. Tak zdarma k tomu je Grok 3,5 od Elona Muska a já osobně používám prostě od Antropiku Clouda, ale je to hrozně na vkusu. Nejrozšířenější je chat GPT. eh, tady Cloud je i levnější, jo a děláte, vidíte, že ten deep research udělal na 370 zdrojem a třeba. Antropic je firma, která se nejvíc zabývá bezpečností. Mně se líbí i jejich etika, ale ty top modely od těch firem jsou v podstatě vyrovnaný. Na to jsem se chtěl zep, Ano."}, {"StartTime": 2809000, "EndTime": 2809000, "VoiceStart": 2809000, "VoiceEnd": 2809000, "Speaker": "Speaker 2", "text": "Můžu se ještě zeptat? Chci se zeptat, eh, jakej vlastně model eh, je nejlepší na to, když bych potřeboval, mám nějakej 100 stránkovej dokument, kterej má prostě nějakou strukturu. Eh, jsou to napsaný nějaký požadavky, je to v PDFku většinou a teďka když to do toho, když když, abych to vložil a zároveň, aby to vlastně jakoby ten text načetl, porozuměl a já si mohl jakoby otázkama vybírat to nejnutnější, co se mě vlastně týká."}, {"StartTime": 2839000, "EndTime": 2839000, "VoiceStart": 2839000, "VoiceEnd": 2839000, "Speaker": "Speaker 1", "text": "Tenhle. Eh, budete to za sem mít v prezentaci. Je to ten zdarma Google AI Studio. Eh, dostanu se i k tady tomuhle tahadlu. Ten má milion tokenů. Eh, já tady mám, ten zpracovával třeba analýze, potřebuju analýze, tady. Já jsem tuhle analýzu, jo, já třeba řeknu, jak my fungujeme. My všechny na hovory nahráváme nebo schůzky s klientama, stejně jako tenhle náš meeting to školení nahráváme. Potom to AI přepisuje a já jenom na základě těch přepisů jsem nechal AI zpracovat tady těhle těch, já nevím, to je asi 120 stran. A to dělá"}, {"StartTime": 2880000, "EndTime": 2880000, "VoiceStart": 2880000, "VoiceEnd": 2880000, "Speaker": "Speaker 1", "text": "nominal. Celý to udělal a můžete to to ronit takhle celý vložit. Ctrl A, Ctrl C. Ctrl A, Ctrl C a normálně. Když to je normálně, když to PDF je normálně naskenovaný, dokáže si to vybrat ten text z toho? Speaker 2 [48:10] Když to PDF normálně, když to PDF je normálně naskenovaný, dokáže si to vybrat ten text z toho? Speaker 1 [48:14] Eh, jo, ale na to už si budete muset, já vás naučím i psát programy za chvíli, ale vy mu to musíte nebo si to musíte rozsekat. Vy tam nedáte 100 obrázků. Jo, ale on umí normálně i číst obrázky za chviličku. Ukážu, jo, ale umí Dokonce do Gemini a já se teď podívám, nevím jestli jestli Invoice PDF, jestli když mu tam dám PDFko, on to asi, on to asi dá. Jo. Jo, já takhle to můžeme normálně zkusit. Dáme tady file, eh, když je jedno, že to je v obrázku jako, jo? Když sežere PDF se 100 stránkama, tak se s tím normálně budete moct bavit. Popřípadě by vám řekl, že to je na na něj moc kontextů, ale normálně dáte tady plus, dáte tady upload file a teď jsem to dal jsem to dal? Speaker 2 [48:34] Mhm. Speaker 1 [48:43] Jo. Speaker 2 [48:43] Jo. Speaker 1 [48:44] Jo. Speaker 2 [48:48] Takže prakticky, když já mu to tam jakoby, když já mu to normálně nahraju, on to dokáže nějakým způsobem analyzovat a já se ho potom po tý analýze tohohle nějakýho dokumentu, se ho zeptám, ale potřebuju tady zjistit, kde se objevuje třeba nevím, otázka na kvalifikaci nějakýho člověka, on mi to řekne, je to tady ten artikl, stránka todle a můžu se o tom bavit dál. Speaker 1 [48:49] Uhm. Uhm. Speaker 2 [48:59] Uhm. Speaker 1 [49:07] Takže prakticky, když já mu to tam jakoby, když já mu to normálně nahraju, on to dokáže nějakým způsobem analyzovat a já se ho potom po tý analýze tohohle nějakýho dokumentu, eh, se ho zeptám, hele, potřebuju tady zjistit, kde se objevuje třeba nevím, otázka na kvalifikaci nějakého člověka a on mi to řekne, je to tady ten artikl, stránka tohle a můžu se o tom bavit dál. Speaker 2 [49:08] Uhm. Speaker 1 [49:30] Jo, jo, přesně tak. Vidíte, že tadyhle 100 stránek, co jsem mu teď dal v PDFku, má jenom 42 000 tokenů. Což je hodně málo, jo. 42 000 tokenů je z toho milionu dost malá část. Čím víc byste se potom blížil, teď teď si to ukážeme. Řekni mi, řekni mi o čem je ten dokument, co jsem ti dal. Eh, a řekni mi, co je v sekci tři. Speaker 2 [49:59] Mhm."}, {"StartTime": 3000000, "EndTime": 3000000, "VoiceStart": 3000000, "VoiceEnd": 3000000, "Speaker": "Speaker 1", "text": "normálně se s ním můžete bavit. A tenhle je na to, je od Googlu, oni nejlíp pracujou s datama a s velkejma datama. A tohle v chat GPT už už ani možná nedocílíte. Jako oni říkaj, Tam je rozdíl v tom interfacu, v tom prohlížeči, jak se to chová a potom na API. A tam v prohlížeči to má ještě omezenější ten kontext. A eh tady tohle je skutečně jako poměr cena výkon vzhledem ještě k tomu, že je to zadarmo, tak eh, úplně to nejlepší. Do toho se počítají i jeho přemýšlecí tokeny, to jsem vám teď chtěl říct. Vidíte, že to tady roste a on mi ještě nic jako ani neřekl. Protože on si tady interně přemýšlí. Já se na to můžu podívat. Co on si. Můžu se, on říká, ano. přemýšlení, jestli oni jako má vliv třeba rychlost internetu nebo výkon počítače na tak ne, ne, ne, absolutně ne. To se všechno počítá na serverech eh, jinde. Tady v tomto případě někde na Googlích, já nevím, jestli tady v Evropě nebo i v Americe, vůbec to absolutně funguje vám to i z telefonu. Eh, můžete si eh, jako já mám jenom pro zajímavost třeba můžu ukázat, já tady můžu vzít telefon. Teď nevím, asi asi mě vidíte nebo vidíte jenom prezentaci. Já to můžu vypnout na chvíli. Jo. Já mám v telefonu Gemini a je to třeba dobrý do auta, jo? Když už se tady bavíme o takovejch věcech a i fotky tam můžete dávat a bavit se i online jako kamerou. Počkej, to já se asi budu muset přehodit. Kdepak vás mám? Tak. Jo? Mám v ruce normálně telefon, mám tam Gemini od Googlu, zapnu si tady, zapnu si tady hlasovej mód, to je takový čudlítko. A já jsem to i ukazoval na jinejch školeních a eh a normálně se s ním můžu bavit. Ahoj Gemini, seš tam?"}, {"StartTime": 3114000, "EndTime": 3114000, "VoiceStart": 3114000, "VoiceEnd": 3114000, "Speaker": "Speaker 2", "text": "Ahoj. Jak se máš? Co tě dneska zajímá?"}, {"StartTime": 3120000, "EndTime": 3120000, "VoiceStart": 3120000, "VoiceEnd": 3120000, "Speaker": "Speaker 1", "text": "že můžete dávat dvě AI proti sobě, proto já mám 4K monitor, proto vám to na začátku připadalo, že je to malý. Eh, já mám rozdělený okno a vy je můžete klidně, když konzultujete právo nebo něco, tak to házet jakoby za jednu stranu i za druhou, jo. To často doporučuju, že jedna je obhájce a jeden je, jedna je žalobce. Pro zjednodušení jedna, jedna strana a druhá je druhá a vyříkáte, jaké jste třeba obhájce a jaké námitky bude mít žalobce a už se připravujete dopředu na to a ona si vyhledává na internetu, eh, zpracovává ty data a samozřejmě tady u toho výzkumu třeba potom konkrétně u Googlu, on vyhodí veškerý zdroje. Tady je vidět veškerý zdroje, ze kterých on čerpá, jo. Samozřejmě není zaručeno a ta AI to nemá jak poznat a proto my tady máme správce AI. Já už se tím zabývám rok, kdy todleto absolutně celý píše AI, vyhledává si témata, generuje obrázky, tak není zaručeno, že ona nenajde nějaký zdroj, který je jako nerelevantní, že jo. A to je"}, {"StartTime": 3240000, "EndTime": 3240000, "VoiceStart": 3240000, "VoiceEnd": 3240000, "Speaker": "Speaker 1", "text": "pro lidi dost složité vědět, jestli Echo 24 píše pro politickou stranu AB a nebo ne. To je prostě a ta AI najde za relevantní, jo. To je velký problém. můžete jí zkusit omezovat, že má chodit jenom na nějaké relevantní nebo si předpřipravit i seznam zdrojů, když ji pouštíte na internet a je to všechno jako o nějaké datové přípravě. A jak jste teď viděli, tak třeba ten sto stránkovej dokument, má má nějakých 40 000 tokenů, což je pořád málo. Říkám, pro představu je to 1,5 násobku všech Harry Potterů. Když se s Gemini dostanete nad půl milionu tokenů, tak už jako docela ztrácí výkon. Tomu se pak říká Needle in the Haystack, jako jehla v kupce sena, kdy oni tomu dají skutečně všechny ty Harry Poterry a zeptají se ho na na nějakou úplně detailní věc z prostředku těch knih, aby zjistili, jestli to zjistí nebo ne tu informaci. Jenom pro představu tam se propočítává váha každého slova s každým. Až tak je to náročné."}, {"StartTime": 3304000, "EndTime": 3304000, "VoiceStart": 3304000, "VoiceEnd": 3304000, "Speaker": "Speaker 2", "text": "Měl bych otázku jednu, když tam takhle předávám různý informace, dávám tam nějaký, dávám tam prostě nějaký soubory, povídám o o něčem konkrétním, ty data zůstávají pouze jako mezi mnou a tím nosičem, se kterým já komunikuju, nebo se to dokáže jakoby vlastně někam nazdílet, někam poslat, nebo jestli to někdo může jakoby ty informace odčerpat taky přes umělou inteligenci."}, {"StartTime": 3332000, "EndTime": 3332000, "VoiceStart": 3332000, "VoiceEnd": 3332000, "Speaker": "Speaker 1", "text": "Eh, tak, ty firmy všechny tvrdí, tak je to Google, má jestli máte Gmail nebo jestli jde o to, které firmě věříte. Jestli máte mail na Gmailu, tak jsou ty maily všechny proskenované a nejenom to, že vy odesíláte mail, ale i když přijde od někoho, že je ten mail, já nevím, od vašeho partnera byznysového a přijde vám tam mail, tak si to Google přečte, že jo. Tak stejným způsobem ty data zůstávají u Googlu."}, {"StartTime": 3360000, "EndTime": 3360000, "VoiceStart": 3360000, "VoiceEnd": 3360000, "Speaker": "Speaker 1", "text": "Googlu. Oni tvrdí, že na těch datech netrénují, dokonce to mají v podmínkách. To znamená, že ty data, který vy tam dáváte, by se neměly nikomu dostat, je to v podmínkách těch firem, eh, stejně jako je to u Anthropicu, u chat GPT, čili u Open AI a stejně tak u Googlu. Existuje možnost, pokud vaše firma o to má zájem, tak existujou open sourceový modely, to jsou AI, který eh, jsou vaše. Vyrábí je třeba eh, Facebook, Meta, pak je vyrábí Mistral, to je jedna francouzská firma, jediná v Evropě. A pak je vyrábějí Číňani, to jsou modely, který jsou odříznutelný od internetu, můžete se je dokonce dát do telefonu, odříznout to od internetu a ten model je, ta AI je přímo v tom zařízení a ty data nikam netečou. Tak to tečou k těm firmám, které eh, tvrdí, že na nich netrénují."}, {"StartTime": 3416000, "EndTime": 3416000, "VoiceStart": 3416000, "VoiceEnd": 3416000, "Speaker": "Speaker 2", "text": "Jak je chytrý čím potom tady tu soukromou AI?"}, {"StartTime": 3419000, "EndTime": 3419000, "VoiceStart": 3419000, "VoiceEnd": 3419000, "Speaker": "Speaker 1", "text": "Ta už je Ta už je hotová."}, {"StartTime": 3420000, "EndTime": 3420000, "VoiceStart": 3420000, "VoiceEnd": 3420000, "Speaker": "Speaker 2", "text": "ta už je hotová. A modely, který přijdou za za rok nebo nějaký novinky, to se?"}, {"StartTime": 3427000, "EndTime": 3427000, "VoiceStart": 3427000, "VoiceEnd": 3427000, "Speaker": "Speaker 1", "text": "Vyměníte ten model. Jo, oni prostě tady lama, tohle je od, teď je lama 4, tohle je od, eh, Facebooku. Můžete si to potom, běží to na grafickejch kartách, čili hráči počítačovejch her jsou na tom líp. A eh, tydle modely jsou prostě vaše. Je to vaše AI, nic za to neplatíte jenom za ten, za to zařízení, na kterým to běží. Eh, data nikam neodtíkají. Oni jsou o něco horší ty modely, jo, ale zase jde to rychle dopředu, takže tam, čím je to chytřejší model, tím potřebuje náročný, náročnější na výpočet a tím potřebuje eh, větší hardware, řekněme. Ty modely, co jdou pustit na telefonu jsou tak jako na překládání a tak jako na máma drobný popovídání, řekněm. Jo. A pak jsou modely na obrázky, na videa,"}, {"StartTime": 3480000, "EndTime": 3480000, "VoiceStart": 3480000, "VoiceEnd": 3480000, "Speaker": "Speaker 1", "text": "Nevím, jestli jste teď viděli, co je schopnej s tím dělat Google, eh, to je neroz nerozeznatelné teď už to video, že, že ho dělala umělá inteligence i se zvukem. Jo, tak to jsem chtěl upozornit jenom, abyste si dávali pozor. Teď zase proběhl nějakej článek, že nějaká holka zkopírovala hlas svý mámy a zavolala do školy a zavolala svýmu tátovi a omluvila se a tak, jo. Tohle všechno jde úplně bez problémů dělat. Jo, ty video modely jsou nerozpoznatelný. To znamená, že za chvíli, co vidíte na internetu, eh, nebudete nikdy vědět, jestli to je pravda nebo ne. A, eh,"}, {"StartTime": 3517000, "EndTime": 3517000, "VoiceStart": 3517000, "VoiceEnd": 3517000, "Speaker": "Speaker 2", "text": "Já jsem se tady hlásil, taky mám dotaz, můžu se zeptat."}, {"StartTime": 3519000, "EndTime": 3519000, "VoiceStart": 3519000, "VoiceEnd": 3519000, "Speaker": "Speaker 1", "text": "Povídejte, určitě, hned, hnedka povídejte."}, {"StartTime": 3521000, "EndTime": 3521000, "VoiceStart": 3521000, "VoiceEnd": 3521000, "Speaker": "Speaker 2", "text": "Nevím, jestli teda už jakoby trošku nepřeskočím, ale bylo to možná asi taky eh, k tomu původnímu dotazu eh Lukáše. Eh, když bude nás používat víc, eh jeden chat GPT, protože my to tak máme tak trošku rozdělený, že třeba nějaký, třeba naše marketingový oddělení, prostě jsme tam třeba tři lidi, který ho můžeme už používat dohromady. Ono si to vlastně pamatuje ty informace, který eh tam zadáváme my naše oddělení dohromady. Kolik, jak byste to viděl, kolik lidí tak dohromady může maximálně eh používat třeba jednu, jeden přístup k chatu, aby se už, abysme se jakoby netloukli s nějakýma informacema, který do toho o sobě dáváme a"}, {"StartTime": 3564000, "EndTime": 3564000, "VoiceStart": 3564000, "VoiceEnd": 3564000, "Speaker": "Speaker 1", "text": "Vůbec to vůbec je absolutně nezávislý. Jak jsem říkal, eh, vy můžete tady tu memory vypnout, pokud používáte todle. Tady je personalizace a tady je vypnout memory. Jo."}, {"StartTime": 3577000, "EndTime": 3577000, "VoiceStart": 3577000, "VoiceEnd": 3577000, "Speaker": "Speaker 2", "text": "Vidíme teďka?"}, {"StartTime": 3578000, "EndTime": 3578000, "VoiceStart": 3578000, "VoiceEnd": 3578000, "Speaker": "Speaker 1", "text": "Jo, pardon, to je takový můj neduh a nešvar. Vždycky, když něco prezentuju, tak potom Tak, tady eh v tom nastavení, já nevím, jestli to jde dát česky. Já to mám v angličtině, ale jo language autodetect, tak já zkusím tady dát češtinu. Tak super. Tak vy to budete mít tady per"}, {"StartTime": 3600000, "EndTime": 3600000, "VoiceStart": 3600000, "VoiceEnd": 3600000, "Speaker": "Speaker 1", "text": "nastavení, personalizace, paměť a já jsem jí vypl. Jo? Tady jde zapnout. Když jí vypnete, tak v podstatě jenom ty chaty zůstávají tady, jo? Tady je."}, {"StartTime": 3615000, "EndTime": 3615000, "VoiceStart": 3615000, "VoiceEnd": 3615000, "Speaker": "Speaker 2", "text": "Tady můžeme samozřejmě vymazat."}, {"StartTime": 3618000, "EndTime": 3618000, "VoiceStart": 3618000, "VoiceEnd": 3618000, "Speaker": "Speaker 1", "text": "Můžete odstranit, ano. Archivovat, omazat, vymazat, dávat si to do složek a normálně v nich vyhledávat."}, {"StartTime": 3626000, "EndTime": 3626000, "VoiceStart": 3626000, "VoiceEnd": 3626000, "Speaker": "Speaker 2", "text": "No já bych to spíš viděla jako takovou pozitivní věc, že když si prostě vlastně do toho do tý naší skupiny tam jakoby nacvakáme eh, jakoby co vlastně nás zajímá, o co, o co nám vlastně jakoby obecně jde v tý naší, v rámci tý naší skupině marketingu, protože samozřejmě, já nevím, eh, finanční oddělení bude používat úplně jiný zdroje, úplně jiný potřeby pro použití umělé inteligence, tak v našem případě třeba, když si tam vlastně už předurčíme, jestli marketingový, já nevím, manažer, pracuju, dělám to a to, pracuju s tím a tím a a rád používám to a to nějaký zdroje, tak eh."}, {"StartTime": 3666000, "EndTime": 3666000, "VoiceStart": 3666000, "VoiceEnd": 3666000, "Speaker": "Speaker 1", "text": "To, ta vlastně To ta personalizace je trošku jinde. Tady je eh a teď vlastní pokyny. Tady potom jsou vlastní pokyny, ale to tam jde nastavit jenom jednou, jo? Eh, jak by ti chat GPT měl říkat, co děláš, jaké vlastnosti by chat GPT měl mít. Tady vy ho vlastně nastavíte a pokud si to nastavíte v marketingovém oddělení, to je o personu, to tam tady se nastavuje persona znova. Personalizace a vlastní pokyny. To je dát zapnout a já jsem tady třeba měl, že na mě mluví ve verších jako pirát, abych to jako demonstroval a on to skutečně pak dělá, jo? A fakt se tak chová, vy můžete tady nastavit, že je marketingový specialista a on, je to pro něj dost důležité, i když se to zdá jako."}, {"StartTime": 3711000, "EndTime": 3711000, "VoiceStart": 3711000, "VoiceEnd": 3711000, "Speaker": "Speaker 2", "text": "Jo."}, {"StartTime": 3712000, "EndTime": 3712000, "VoiceStart": 3712000, "VoiceEnd": 3712000, "Speaker": "Speaker 2", "text": "No to, to si právě myslím, že to je právě důležitý, akorát že to je vlastně něco jinýho, než jsme se bavili původně a to jsem nevěděla, že takhle tady v tom rozdíl, takže tady to je ta vlastní personalizace."}, {"StartTime": 3720000, "EndTime": 3720000, "VoiceStart": 3720000, "VoiceEnd": 3720000, "Speaker": "Speaker 1", "text": "podu, to je jedno, jo? Eh, něco, co vám funguje, bohužel před GPT to nemá, jo? Eh, Antropik to má, že si to předpřipravujete do nějakých svejch složek a potom to můžete používat znova. Náš tým my používáme ještě jiný rozhraní, ale v podstatě principielně oni mají předpřipravený třeba ten prompt, jak se tomu říká, na nabídku. Oni řeknou, oni ví, jak má on dělat nabídky, takže vezme naše třeba obchodní ředitelka, vezme ten prompt, Ctrl A, Ctrl C, Ctrl V a dá mu potom jenom data z toho rozhovoru s klientem, který jsou přepsaný Ctrl A, Ctrl C, Ctrl V a on to zpracuje. Takže takhle, když si to předpřipravíte, protože to je nějaká drobná práce, ale zase na to používejte, eh, AI, já to já to klidně ukážu. Eh, jaké jsou vlast vlastnosti špičkového marketingového p"}, {"StartTime": 3840000, "EndTime": 3840000, "VoiceStart": 3840000, "VoiceEnd": 3840000, "Speaker": "Speaker 1", "text": "praxi po 10 letech praxe."}, {"StartTime": 3846000, "EndTime": 3846000, "VoiceStart": 3846000, "VoiceEnd": 3846000, "Speaker": "Speaker 1", "text": "Používat AI na AI je to nej jakoby nejrozumější a je to i doporučený."}, {"StartTime": 3852000, "EndTime": 3852000, "VoiceStart": 3852000, "VoiceEnd": 3852000, "Speaker": "Speaker 1", "text": "Jo. A já mu potom to, co on řekne, vezmu a řeknu mu: Chovej se takto."}, {"StartTime": 3859000, "EndTime": 3859000, "VoiceStart": 3859000, "VoiceEnd": 3859000, "Speaker": "Speaker 1", "text": "Jo, chovej se podle tohole."}, {"StartTime": 3862000, "EndTime": 3862000, "VoiceStart": 3862000, "VoiceEnd": 3862000, "Speaker": "Speaker 1", "text": "A, eh, můžu mu dát, protože já tady mám ten nejpomalejší model, tak mu to trvá, jo."}, {"StartTime": 3869000, "EndTime": 3869000, "VoiceStart": 3869000, "VoiceEnd": 3869000, "Speaker": "Speaker 1", "text": "Ale, eh, takhle prostě, protože jsou to skvělý generátory textu, tak než by člověk tohlencto napsal, tak, eh, samozřejmě je lepší AI používat na AI, kdy já to všem vždycky připodobňuju, že je to jako moji základní myšlenku, když já mám nějakou základní kuličku, chci skvělý marketingový text a je to takhle jednoduché, chci skvělý marketingový text, já nevím, o, o kočkách a je to takhle jednoduchá myšlenka, tak já si ji s ním rozvinu. Jo? Já si, já mu řeknu, chci skvělý marketingový text o počkách, co všechno, na co všechno mám myslet a už se normálně s ním povídám."}, {"StartTime": 3909000, "EndTime": 3909000, "VoiceStart": 3909000, "VoiceEnd": 3909000, "Speaker": "Speaker 1", "text": "A on je skutečně jako specialista natrénovanej na veškerých marketingových datech, veškerých právních datech, s tím, co jsme si uvedli na začátku, že čím víc relevantních informací v textové podobě, popřípadě i v obrázcích, mu dodám, v případě Jeminí umí i videa, jo, tak, eh, tím je přesnější a lepší."}, {"StartTime": 3930000, "EndTime": 3930000, "VoiceStart": 3930000, "VoiceEnd": 3930000, "Speaker": "Speaker 1", "text": "A tohle je pořád jenom o té praxi, že to zkouším, že si s tím hraju, že zjišťuju třeba hlavně proč mi to jako, eh, říká jiný věci než má. To je nejčastěji, že lidi si neuvědomujou, že mají ten cutoff, že že jako neznají všechny data bez vládání na internetu a nebo používají ty modely zdarma, no, které jsou jako skutečně úplně, to je úplně nepoužitelné za nás."}, {"StartTime": 3954000, "EndTime": 3954000, "VoiceStart": 3954000, "VoiceEnd": 3954000, "Speaker": "Speaker 1", "text": "Protože to je i ze zkušenosti, že to říká úplně jiné věci, jo."}, {"StartTime": 3958000, "EndTime": 3958000, "VoiceStart": 3958000, "VoiceEnd": 3958000, "Speaker": "Speaker 1", "text": "Stačí to takhle k tomu dotazu."}, {"StartTime": 3960000, "EndTime": 3960000, "VoiceStart": 3960000, "VoiceEnd": 3960000, "Speaker": "Speaker 1", "text": "Já mám ještě k těm modelům ještě. Vy jste teďka dal 4.5 model, eh, když tam je 40 O O nula nebo O tři nebo já nevím, O čtyři mini a tak dále. Eh, jaký, jakej, přepíná se to automaticky nebo jaký si máme dát model? Jak poznáme třeba, kterej model na to použít?"}, {"StartTime": 3984000, "EndTime": 3984000, "VoiceStart": 3984000, "VoiceEnd": 3984000, "Speaker": "Speaker 2", "text": "Eh, vždycky je to to nejvyšší číslo zjednodušeně. A jak jsem říkal, eh, když je tam mini nebo flash, nebo když to nějakým slovem zavání jako rychlostí, u Chat GPT tomu říkají Mini, u Google tomu říkají Flash a teď má ještě jako hyper Flash, když já to tady přepnu na Gemini 2,5 Flash, tak eh, eh, napiš, jo, napiš o kapitole čtyři. Tenhle přemýšlel 69 vteřin, jo? 69 vteřin mu trvalo vygenerování tý odpovědi a tomuhle to bude trvat jako o dost míň. Protože je hloupější, ale rychlejší. To je jejich jediný jako rozdíl, že oni, eh, teď začal přemýšlet a teď jako to říká odpověď."}, {"StartTime": 4032000, "EndTime": 4032000, "VoiceStart": 4032000, "VoiceEnd": 4032000, "Speaker": "Speaker 1", "text": "Můžu se ještě rychle k tomu zeptat? Má v dnešní době teda jakoby vůbec nějakou, nějakej smysl platit si Adobe Firefly na generování nějakých obrázků, když mi to může dělat nějaká umělá inteligence, kterou už mám v rámci teda, my máme teda ten pacenou verzi chat GPT."}, {"StartTime": 4051000, "EndTime": 4051000, "VoiceStart": 4051000, "VoiceEnd": 4051000, "Speaker": "Speaker 2", "text": "Jo, a hodně záleží, tam je to hrozně moc o vkusu. Jako, eh, co, za mě nemá, pokud vám to generuje hezké obrázky, tak třeba jo, ale tady todleto, co vidíte na tý zprávě AI, tak to je všechno chat GPT, todleto, jo? To všechno generuje on. Teď už nově. A on je schopnej upravovat, jako jeden z prvních modelů je schopnej upravovat obrázky přímo jako do toho, že mu řekneš."}, {"StartTime": 4080000, "EndTime": 4080000, "VoiceStart": 4080000, "VoiceEnd": 4080000, "Speaker": "Speaker 1", "text": "Teď a teď kterej model?"}, {"StartTime": 4081000, "EndTime": 4081000, "VoiceStart": 4081000, "VoiceEnd": 4081000, "Speaker": "Speaker 1", "text": "Můžeme to zkusit úplně jako, když"}, {"StartTime": 4084000, "EndTime": 4084000, "VoiceStart": 4084000, "VoiceEnd": 4084000, "Speaker": "Speaker 1", "text": "fotorealistický."}, {"StartTime": 4085000, "EndTime": 4085000, "VoiceStart": 4085000, "VoiceEnd": 4085000, "Speaker": "Speaker 1", "text": "To záleží jako, kterou AI na to použiju."}, {"StartTime": 4089000, "EndTime": 4089000, "VoiceStart": 4089000, "VoiceEnd": 4089000, "Speaker": "Speaker 1", "text": "Konkrétně na obrázky je to dost jako, je to dost specifický, existuje Replicate se to jmenuje."}, {"StartTime": 4093000, "EndTime": 4093000, "VoiceStart": 4093000, "VoiceEnd": 4093000, "Speaker": "Speaker 1", "text": "To je, kde mají"}, {"StartTime": 4200000, "EndTime": 4200000, "VoiceStart": 4200000, "VoiceEnd": 4200000, "Speaker": "Speaker 1", "text": "všechny modely, kterejch je spousta a eh, tam si můžete jako na generování obrázků vybírat ten zrovna nejvhodnější, jo, jestli chcete kreslený, jestli chcete, já nevím, komixově nějak zabarvený, jestli chcete eh fotorealistický. A to vám třeba pro představu ukážu, jo, to VO, o kterým jsem mluvil, co je AI schopná dělat. Ale tady ten samej prompt, kterej jsem si nechal vylepšit ještě, já tady mám vylepšování promptu. A on to píše fotorealisticky, tady potom mám tohohle papouška. Takže tam hodně záleží na modelu AI. Jo, jenom pro představu, abyste tušili, kde ta AI teď je, tak tady eh"}, {"StartTime": 4251000, "EndTime": 4251000, "VoiceStart": 4251000, "VoiceEnd": 4251000, "Speaker": "Speaker 1", "text": "Tady, já nevím, jestli jestli slyšíte nebo neslyšíte teď. To, já počkejte, já budu muset to vypnout a dát, že chci prezentovat. Eh jo a teď share audio, jo. A teď to mají, já si to musím pustit."}, {"StartTime": 4270000, "EndTime": 4270000, "VoiceStart": 4270000, "VoiceEnd": 4270000, "Speaker": "Speaker 2", "text": "They're watching the North exit. Use the service tunnel."}, {"StartTime": 4276000, "EndTime": 4276000, "VoiceStart": 4276000, "VoiceEnd": 4276000, "Speaker": "Speaker 1", "text": "Jo, osmivteřinový videa, eh"}, {"StartTime": 4279000, "EndTime": 4279000, "VoiceStart": 4279000, "VoiceEnd": 4279000, "Speaker": "Speaker 3", "text": "This ocean, it's a force, a wild untamed might, and she commands your awe with every breaking light."}, {"StartTime": 4288000, "EndTime": 4288000, "VoiceStart": 4288000, "VoiceEnd": 4288000, "Speaker": "Speaker 1", "text": "Teď jsme tady, před rokem to ne, to kreslilo šest prstů. Jo, jenom pro představu, existujou eh"}, {"StartTime": 4299000, "EndTime": 4299000, "VoiceStart": 4299000, "VoiceEnd": 4299000, "Speaker": "Speaker 1", "text": "Existuje Humanity Last Exam, to je nějakej, jenom to jo, zaprezentuju. Moment. Já si tady jenom zapnu. Tak, to je nějakej benchmark. Poslední zkouška lidstva, tomu říkají, kde je to jsou absolutně nejsložitější věci od PhD lidí ze všech oborů a AI je teď na nějakej"}, {"StartTime": 4320000, "EndTime": 4320000, "VoiceStart": 4320000, "VoiceEnd": 4320000, "Speaker": "Speaker 1", "text": "jejich eh 21 %, Gemini má 2,5 Pro. Jo, umí to věci na PhD level z programování, matematiky, dokonce tohodle roku je to nejlepší programátor na světě, matematiku už je snad jenom 100 na světě, který jsou chytřejší než AI. A eh, jde to jako fakt hodně rychle dopředu, eh, za chvíli nebo mi už tady a teď umíme automatizovat téměř veškerou lidskou práci u počítače, co se dělá s datama, tak jde automatizovat pomocí agentů, pomocí prostě to je ta věc, co mi vytvářela. Ono ono to pak jde pustit ve vašem počítači, že vlastně nemusíte dělat nic, že mu jenom řeknete najdi, vytvoř mi Excel, vytvoř mi nad tím graf. Pak se to ovládá trošku jinak, to jsem vám potom chtěl ukázat. kdy eh, jak tam máte tu analýzu těch Excel souborů, tak vy mu jenom řeknete, kde je ten Excel soubor a on vám z toho může tam měnit data, může z toho obohacovat ty data o data z internetu. V podstatě prakticky jakoukoliv práci, co děláte u počítače, tak to je AI ti schopná zvládat. Open AI s tím, já jsem to třeba na minuly prezentaci ukazoval operátora, on normálně chodí, kliká do prohlížeče, za chvíli to vydá i Google a chová se jako člověk u toho prohlížeče, takže kliká, vyhledává, nakupuje letenky, eh, nakupuje v obchodě, prakticky cokoliv. Jo, teď jsme na týhle úrovni, příští rok mají nastoupit inovátoři, to znamená, že to má dělat vynálezy, který lidi jako, že to má vynalézat věci samo. Tam ještě nejsme, jo. To je ještě ještě jako jinej příběh. Ale nevím, jestli jsme teď zodpověděli všechno k tomu. Dobrý den, já mám ještě taky otázku, je někde v nastavení těch modelů datum toho cuttoff přesně abych to zjistila. To se zjišťuje na internetu. A teď každej model. Já jsem ho nenašla."}, {"StartTime": 4440000, "EndTime": 4440000, "VoiceStart": 4440000, "VoiceEnd": 4440000, "Speaker": "Speaker 1", "text": "jo. Každý model to má jiný. Eh, prakticky chat GPT to má 2000, April 2023, čili eh, duben 2023, jo, kupodivu, protože to jsou oni jsou starý datově ty ty nejchytřejší modely. A ta Gemini to třeba má, jako nejnovější jsou z, co mají nejnovější znalosti, tak je to je to prosinec minulýho roku, myslím, jestli se nepletu."}, {"StartTime": 4471000, "EndTime": 4471000, "VoiceStart": 4471000, "VoiceEnd": 4471000, "Speaker": "Speaker 1", "text": "Mhm. Proto proto si to všechno vyhledává na internetu, jo. Proto jako ty nový informace si to musí vyhledávat na internetu, protože on je nezná, on je nemá ve svým mozku, on je potřebuje získat nějakým způsobem. Dobře."}, {"StartTime": 4483000, "EndTime": 4483000, "VoiceStart": 4483000, "VoiceEnd": 4483000, "Speaker": "Speaker 1", "text": "A proto na to tady má ten search the web, jo, nebo já když mu to i řeknu, najdi na internetu, najdi na internetu dnešní počasí v Ostravě. Třeba, tak normálně hned použije nástroj a najde ho, jo."}, {"StartTime": 4504000, "EndTime": 4504000, "VoiceStart": 4504000, "VoiceEnd": 4504000, "Speaker": "Speaker 1", "text": "Takže proto jim dávají ten přístup na internet, protože oni to nemají. Ale zase, jak jsem říkal, tam je fakt pozor na to, na jakých webech to jako hledá. Těch deep deep research vám to řekne, tady mi to třeba neřekl, kde to našel ty data, jo. To je prostě problém. On to může mít od něčeho, čemu nevěřím. U toho deep researche mi to řekne, odkaď to je ty data. Tam je to dohledatelný, tady jsme si to ukazovali. Tady je prostě soupis. Když já dám export a dám export to Docs, tak se mi to otevře normálně v Google Docs a tam by měly být ty datový zdroje potom. abych jako si mohl ověřit, že je to to správný. To ověřování bohužel nebo je to jak ověřovat práci prostě nějakýho juniora, protože, jo, tady je to"}, {"StartTime": 4560000, "EndTime": 4560000, "VoiceStart": 4560000, "VoiceEnd": 4560000, "Speaker": "Speaker 1", "text": "to se psaný, kde on ty data našel."}, {"StartTime": 4565000, "EndTime": 4565000, "VoiceStart": 4565000, "VoiceEnd": 4565000, "Speaker": "Speaker 1", "text": "A, eh, proto je potřeba to používat a naučit se s tím, jak mu třeba ty zdroje omezovat nebo jaký mu dávat, eh, zadání, aby hledal jenom na těch webech, eh, kde chci."}, {"StartTime": 4576000, "EndTime": 4576000, "VoiceStart": 4576000, "VoiceEnd": 4576000, "Speaker": "Speaker 1", "text": "Nejlíp, nejlíp, my jsme to zkoušeli minule na jiným školení a nejlíp to skutečně dodržoval chat GPT tady s modelem O3. My jsme po nich chtěli litevský stránky o, to bylo GS, dělají GScom, jo, tak ty chtěli litevský stránky o kloubní výživě."}, {"StartTime": 4594000, "EndTime": 4594000, "VoiceStart": 4594000, "VoiceEnd": 4594000, "Speaker": "Speaker 1", "text": "Ostatní modely začly chodit na jiný, než jako litevský stránky. Chat GPT O3 to dodržel a hledal jenom na litevských stránkách a jenom relevantní informace."}, {"StartTime": 4605000, "EndTime": 4605000, "VoiceStart": 4605000, "VoiceEnd": 4605000, "Speaker": "Speaker 1", "text": "Ale zase, tam velmi složitě se vyznáte v tom, jestli litevská stránka, eh, je relevantní zdroj dat, jo."}, {"StartTime": 4613000, "EndTime": 4613000, "VoiceStart": 4613000, "VoiceEnd": 4613000, "Speaker": "Speaker 1", "text": "Chat GPT používá Bing. To je vyhledávač od Googlu, protože oni jsou teda od vyhledávač od Microsoftu, oni jsou spojení s Microsoftem Open AI, takže používá jako data z Bingu."}, {"StartTime": 4626000, "EndTime": 4626000, "VoiceStart": 4626000, "VoiceEnd": 4626000, "Speaker": "Speaker 1", "text": "To je to, co taky známý o tom, kde on vlastně, jaký vyhledávače a jak to používá."}, {"StartTime": 4633000, "EndTime": 4633000, "VoiceStart": 4633000, "VoiceEnd": 4633000, "Speaker": "Speaker 1", "text": "A, eh, ti cutoffy vás asi už nemusí moc trápit. Vidíte, že on si jakmile tuší, že to jako, je to, je to hodně těžký, no, musíte, když pracujete s vaš s dama vašeho typu a jenom mu je tam dáte. Kdyby to třeba byla nějaká norma teď nebo nebo zákon, norma, zákon a vy víte, že se to změnilo nebo mohlo se to změnit, tak když mu ty data dáte, řeknete mu pracuj s těmihle daty a norma se mohla změnit, vyhledej to na internetu, tak on půjde a vyhledá to."}, {"StartTime": 4667000, "EndTime": 4667000, "VoiceStart": 4667000, "VoiceEnd": 4667000, "Speaker": "Speaker 1", "text": "Ale když byste mu to neřekli, tak začne pracovat z paměti s největší pravděpodobností, jo."}, {"StartTime": 4673000, "EndTime": 4673000, "VoiceStart": 4673000, "VoiceEnd": 4673000, "Speaker": "Speaker 1", "text": "A proto je potřeba si s tím opravdu jako hrát, seznamovat se, učit se a, eh,"}, {"StartTime": 4680000, "EndTime": 4680000, "VoiceStart": 4680000, "VoiceEnd": 4680000, "Speaker": "Speaker 1", "text": "Tam opravdu občas stačí změnit jedno slovo v tom promptu a on se začne chovat jinak. To jsou"}, {"StartTime": 4686000, "EndTime": 4686000, "VoiceStart": 4686000, "VoiceEnd": 4686000, "Speaker": "Speaker 1", "text": "Já těžko to řeknu jinak než, že mám za sebou nějakých, dneska jsem to schválně počítal, 70, 80 000 promptů a, eh, je to, jsou to prostě nuance. A navíc to"}, {"StartTime": 4701000, "EndTime": 4701000, "VoiceStart": 4701000, "VoiceEnd": 4701000, "Speaker": "Speaker 1", "text": "jediný, kde si, kde si k tomu můžete jako šáhnout ve webu, je tady Google AI Studio, je tady takový tahadlo"}, {"StartTime": 4710000, "EndTime": 4710000, "VoiceStart": 4710000, "VoiceEnd": 4710000, "Speaker": "Speaker 1", "text": "magický, jinde to není, než tady, jo? Toto je spíš pro programátory, tady to rozhraní, ale tady mu nastavujete jeho kreativitu."}, {"StartTime": 4719000, "EndTime": 4719000, "VoiceStart": 4719000, "VoiceEnd": 4719000, "Speaker": "Speaker 1", "text": "Když je to na jedna, tak je středně kreativní. Když je to na nula, tak je to dělaný tak, aby to počítalo příklady, řekněme. Vy chcete, aby byl absolutně striktní, absolutně, eh, nevymejšlel si a byl úplně co nejpřesnější, tak to dáte na nula nebo na 0,05."}, {"StartTime": 4735000, "EndTime": 4735000, "VoiceStart": 4735000, "VoiceEnd": 4735000, "Speaker": "Speaker 1", "text": "Když to dáte na dva, tak si můžete psát nějakou, eh, beletrii, nějakou jako knížku a je hrozně kreativní a vymejšlí si, řekněme."}, {"StartTime": 4744000, "EndTime": 4744000, "VoiceStart": 4744000, "VoiceEnd": 4744000, "Speaker": "Speaker 1", "text": "Jo. To to je nikde jinde než, eh, v Google AI Studio neseženete, to je potom jako na API dělaný. Chat GPT to není."}, {"StartTime": 4754000, "EndTime": 4754000, "VoiceStart": 4754000, "VoiceEnd": 4754000, "Speaker": "Speaker 1", "text": "Já tam určitě používejte O3 na, tak na typy, na typy vaší práce, jo, tenhle model. Ani určitě ne O4 mini, je sice rychlej. Jakmile to vypadá, že moc rychlý, tak to v podstatě nechcete, to je takový, eh, takový jednoduchý pravidlo."}, {"StartTime": 4771000, "EndTime": 4771000, "VoiceStart": 4771000, "VoiceEnd": 4771000, "Speaker": "Speaker 1", "text": "Jo, ty rychlý modely jsou na, já nevím. Oni jsou hlavně levný, jo, tam jde o to, že my když to používáme programově na API, tak oni jsou levný, ale vy jako lidi a uživatelé chcete používat to nejchytřejší. Teď je to O3 Pro, pak to bude O teda O3, pak to bude O4."}, {"StartTime": 4788000, "EndTime": 4788000, "VoiceStart": 4788000, "VoiceEnd": 4788000, "Speaker": "Speaker 2", "text": "A jak je teda rozdíl mezi ten, eh, eh, tou 40, 4.0 a O3 teda? Jsem to okamžitě použil."}, {"StartTime": 4793000, "EndTime": 4793000, "VoiceStart": 4793000, "VoiceEnd": 4793000, "Speaker": "Speaker 1", "text": "4.0, 4.0 je úplně ten základní a řekněme nejhloupější model. Je to tady i v tom"}, {"StartTime": 4800000, "EndTime": 4800000, "VoiceStart": 4800000, "VoiceEnd": 4800000, "Speaker": "Speaker 1", "text": "testu vidět že že a teď ani nevím, kde ho mám najít. On je V90 IQ 91 podle tady tohohle. O3 má IQ 133, jo. Nebo 139 měli. Proto říkám, ten základní je úplně jako to se v podstatě nedá používat a potom lidi nadávají na AI, protože používají něco, co je to je mnohem lepší zadarmo používat tady do Google AI Studia 2,5 Pro, kterej i podle toho IQ testu má nějaký 127 nebo něco takovýho. A v pochopení a porozumění a práci s textem jsou jako už dlouho na nějakých jako 180 až 200 IQ, takže ty top modely ale. A ale ten 4O je nejhorší v podstatě. To je úplně to nejhorší, co teď na trhu se vůbec dá sehnat. Proto je potřeba mít něco placenýho. Nebo on Anthropic, tady todlencto jde taky normálně, můžete mít zadarmo Cloda, Claude.ai. A tam jsou akorát docela drsný limity, ale máte to myslím úplně zadarmo. Jo? Ale ta ta verze je placená ta GPT 4 40. Nebo Ne, ta není placená. Ne, ta akorát to nevidíte, oni vám neříkají, že v tý neplacený je to taky 4O, jo. Ale tak jako já mám placenou verzi, ale mám tam i to 4.0 používaný, takže Jo, to jo, to jo, to jo, to jo. To normálně přepnete tady na nahoře, jak jsem vám to ukazoval. Tady to přepnete na O3. Nebo O3 Pro? Jestli máte O3, jestli máte O3 Pro, tak byste, tak musíte mít to předplatný za 200 dolarů měsíčně a O3 Pro je Ten úplně, ten úplně nejchytřejší, co je. Ale tomu to zas hrozně dlouho trvá, jo. Jakoby já tady mám někde nějaký starší."}, {"StartTime": 4920000, "EndTime": 4920000, "VoiceStart": 4920000, "VoiceEnd": 4920000, "Speaker": "Speaker 1", "text": "eh ikdyž tady o třeba SC, jo, tady nějaký kód a teď já nevím, jestli to dělal O3 Pro a tomu trvá prostě 20 minut. Jo. Jo, tohle ještě to to jsou nějaký."}, {"StartTime": 4933000, "EndTime": 4933000, "VoiceStart": 4933000, "VoiceEnd": 4933000, "Speaker": "Speaker 2", "text": "Ale vy jste říkal, že máte nějakej ten Cloud, kterej prostě je za 100 $ a je už napořád, už se nic neplatí."}, {"StartTime": 4939000, "EndTime": 4939000, "VoiceStart": 4939000, "VoiceEnd": 4939000, "Speaker": "Speaker 1", "text": "Eh, měsíčně samozřejmě."}, {"StartTime": 4941000, "EndTime": 4941000, "VoiceStart": 4941000, "VoiceEnd": 4941000, "Speaker": "Speaker 2", "text": "Měsíčně je to za 100 $?"}, {"StartTime": 4943000, "EndTime": 4943000, "VoiceStart": 4943000, "VoiceEnd": 4943000, "Speaker": "Speaker 1", "text": "Jo, to všechny všechny tyhlecty AI jsou placený měsíčně. Jo. Jako žádná na."}, {"StartTime": 4950000, "EndTime": 4950000, "VoiceStart": 4950000, "VoiceEnd": 4950000, "Speaker": "Speaker 1", "text": "Protože se to musí počítat na grafickejch kartách a proto jsem říkal pro představu, že ten počítač na to stojí 10 bilionů korun a ty firmy potřebujou jako si to někde brát ty peníze, i když je to teď jako dost ztrátový nejspíš ještě pro ně. Jenom ten proud, co to žere, se jim skoro ani nezaplatí z toho, co co mají ty, co lidi jako používají."}, {"StartTime": 4971000, "EndTime": 4971000, "VoiceStart": 4971000, "VoiceEnd": 4971000, "Speaker": "Speaker 1", "text": "Cloud Code, to jsem vám chtěl jako finálně ukázat. Máme nějakejch 15 minut. Tak, vy můžete potom si nainstalovat věc, která se jmenuje VSC Code. Je to zase, to je úplně zadarmo, je to od Microsoftu, máme na to návody, dostanete ten návod i v eh, materiálech v tomhletom školení. A eh, tam přijdete, vložíte klíč právě z Gemini, kterej je zadarmo. Je to budete to mít v tomu návodu. Google Gemini. Je tady potřeba vložit klíč, kterej se tady v tom Google AI Studiu generuje na tlačítko nahoře. Tady je Get API key. Dostanete ho zadarmo, zase za nějaký limited akorát. A eh, pak ta věc je ve vašem počítači a je schopná pracovat se souborama na vašem počítači. A já mu můžu říct: vygeneruj mi Excelový soubor eh, s vzorovými daty. A ten soubor prostě bude na mým počítači. On si na to začne psát kód. A eh, začne instalovat věci."}, {"StartTime": 5040000, "EndTime": 5040000, "VoiceStart": 5040000, "VoiceEnd": 5040000, "Speaker": "Speaker 1", "text": "začne psát normálně v počítačovej strojovej kód. On to píše v Pythonu a eh pak je schopnej je i číst. Eh takže mu můžu říct načti si ten soubor. Jo, teď mi vygeneroval soubor, kterej je a teď tam jde. Jo. Normálně můžu jít, vlezu si do těchch souborů. Eh tady homework, teda, tady je. Moment. Tady. Homework programming a tady teď mi to nesmí upadnout. A tady je sample data a tady mi vygeneroval vidíte teď, že je tam Alice Bob Charlie New York a teď já mu řeknu, eh vůbec, vůbec nechci taková to data, udělej eh česká data o městech s počtem obyvatel v různých věkových kategoriích a eh udělej sloupečk se vzorcem, který bude počítat průměrný věk ve městě. Udělej aspoň 20 řádků, čili 20 různých měst. Které tomu. Jo, to je flash, aha, já to tady musím přepnout. Já ho tady přepnu na pro. A eh šup. A on začne. Jo? A začne normálně pracovat. Teď to přepíše ten kód. Přepíše, udělá vytvoří si vzorový data, vytvoří si prostě co je potřeba, můžem nad tím dělat grafy. Prakticky cokoliv, ale tam je to potřeba zkoušet učit se s tím, v naší"}, {"StartTime": 5160000, "EndTime": 5160000, "VoiceStart": 5160000, "VoiceEnd": 5160000, "Speaker": "Speaker 1", "text": "skupině na Discordu, kde to učím jako už tam máme 900 lidí, tak eh, někteří si s tím absolutně zautomatizovali svoji práci a toto vůbec nejsou programátoři, jako vůbec. Jo. Eh, zautomatizovali tím veškerou práci s Excel a eh, no a teď je tam chyba. A tomu hele, už už se to, jo, není tam chyba. Dobrý. Jo, vidíte, že tam normálně udělal i vzorce a můžu s tím normálně pracovat, můžu mu říct, že to má dělat každej tejden, můžu mu říct cokoliv. Protože programuju česky dokonce. Jo. A, je to úplně zadarmo tohle, co vám teď ukazuju. Pouze s nějakejma limitema. Můžu mu říct, že chci nástroj na na překládání, já nevím, na automatický překládání nějakýho webu, úplně co co vás napadne. Můžu mu říct, že chci to vaše PDF každej tejden stahovat z emailu. Ty věci potom mají přístup do emailu, já to mám napojení na, na memory, na nějakou centrální paměť firemní, prostě cokoliv si představíte, tak ten bot je s potom schopnej dělat, protože sedí ve vašem počítači. Ale jeho ekvivalent tady tohle je ten clot code, kterým, který my potom používáme, akorát to vypadá jinde, je to v černý obrazovce a eh, já se prostě s ním bavím, jak s chatbotem akorát přes terminál. Já mu prostě říkám, udělej to, zajdi si na internet, najdi si si dokumentaci, vytvoř mi k tomu obrázek, tady vem tuhle nahrávku, vem to z našeho systému a udělej mi z toho analýzu v podstatě. Jo, ale to je skutečně potřeba nainstalovat to, vzít si ten klíč, kterej se dá sem a začít si s tím povídat. A je to až tak jednoduché, že, eh, máme tam dokonce i nějak i nějakých pár dám, nějaké dámy. Naše obchodní ředitelka si s tím naprogramovala pro Mac aplikaci už někdy před"}, {"StartTime": 5280000, "EndTime": 5280000, "VoiceStart": 5280000, "VoiceEnd": 5280000, "Speaker": "Speaker 1", "text": "příštvrtě rokem a to v životě nic jako nedělala, nikdy nic neprogramovala. A normálně stačí jít a říkat, co chci a pak se toho nebát a říct, eh, a teď je teď a udělej tam dalších pár sloupců relevantních vzorových dat, která by nás mohla zajímat."}, {"StartTime": 5306000, "EndTime": 5306000, "VoiceStart": 5306000, "VoiceEnd": 5306000, "Speaker": "Speaker 1", "text": "Vytvořím si data a pak mu řeknu, co s těma datama chci dělat."}, {"StartTime": 5310000, "EndTime": 5310000, "VoiceStart": 5310000, "VoiceEnd": 5310000, "Speaker": "Speaker 1", "text": "vidíte, že je schopnej tam dávat vzorce, potom to poslat šéfovi e-mailem a jako opravdu cokoliv si vymyslíte, protože umí ovládat počítač, eh, skrz jako programování, skrz kód. Já s tím spravuju servery. Eh, naše firma prakticky jako nedělá teď už programátoři už se nedotýkají kódu, jo."}, {"StartTime": 5332000, "EndTime": 5332000, "VoiceStart": 5332000, "VoiceEnd": 5332000, "Speaker": "Speaker 1", "text": "Už to to není na pořadu dne, teď už se programuje tímhle způsobem."}, {"StartTime": 5340000, "EndTime": 5340000, "VoiceStart": 5340000, "VoiceEnd": 5340000, "Speaker": "Speaker 1", "text": "Jo, takže potom tady přijdu. Tady 11:22 zaktualizoval to."}, {"StartTime": 5349000, "EndTime": 5349000, "VoiceStart": 5349000, "VoiceEnd": 5349000, "Speaker": "Speaker 1", "text": "Eh, kde pak to máme? To zavřu."}, {"StartTime": 5355000, "EndTime": 5355000, "VoiceStart": 5355000, "VoiceEnd": 5355000, "Speaker": "Speaker 2", "text": "Tohle je jakej teda?"}, {"StartTime": 5357000, "EndTime": 5357000, "VoiceStart": 5357000, "VoiceEnd": 5357000, "Speaker": "Speaker 1", "text": "Eh, to se jmenuje Cline. Budete to zase mít v tom. Budete to mít v těch materiálech. Jsou to linky. Je to o je to kombinace VS kodu, VS Code. To je od Microsoftu."}, {"StartTime": 5373000, "EndTime": 5373000, "VoiceStart": 5373000, "VoiceEnd": 5373000, "Speaker": "Speaker 1", "text": "Tady download Visual Studio Code. Je to prostě na všechny systémy. Od Microsoftu je tady ta krabička, do který koukáme. Tady toto, tady ten to logo modrý nahoře vlevo. Vy si tam nainstalujete rozšíření, který se jmenuje Cline, to je jenom rozšíření toho. Do toho rozšíření se dá klíč, který se bere tady od eh Google AI Studia. Ten je zadarmo."}, {"StartTime": 5400000, "EndTime": 5400000, "VoiceStart": 5400000, "VoiceEnd": 5400000, "Speaker": "Speaker 1", "text": "a pak to takhle programuje."}, {"StartTime": 5401000, "EndTime": 5401000, "VoiceStart": 5401000, "VoiceEnd": 5401000, "Speaker": "Speaker 1", "text": "Pak to dělá prostě to, co vám tady ukazuju."}, {"StartTime": 5404000, "EndTime": 5404000, "VoiceStart": 5404000, "VoiceEnd": 5404000, "Speaker": "Speaker 1", "text": "Jo, tady mám nový data, mám tady průměrnou mzdu si vymyslel a já nevím, co si vymyslel."}, {"StartTime": 5410000, "EndTime": 5410000, "VoiceStart": 5410000, "VoiceEnd": 5410000, "Speaker": "Speaker 1", "text": "A teď mu řeknu: "Vygeneruj z jak co všechno, jaké grafy můžeme z dat vygenerovat?""}, {"StartTime": 5417000, "EndTime": 5417000, "VoiceStart": 5417000, "VoiceEnd": 5417000, "Speaker": "Speaker 1", "text": "Je dobrý se ho ptát, protože jaké grafy můžeme z dat vygenerovat."}, {"StartTime": 5425000, "EndTime": 5425000, "VoiceStart": 5425000, "VoiceEnd": 5425000, "Speaker": "Speaker 1", "text": "A tady je plánovací mód."}, {"StartTime": 5428000, "EndTime": 5428000, "VoiceStart": 5428000, "VoiceEnd": 5428000, "Speaker": "Speaker 1", "text": "Je tady to přepnu do plánovacího módu."}, {"StartTime": 5430000, "EndTime": 5430000, "VoiceStart": 5430000, "VoiceEnd": 5430000, "Speaker": "Speaker 1", "text": "Ukažu."}, {"StartTime": 5431000, "EndTime": 5431000, "VoiceStart": 5431000, "VoiceEnd": 5431000, "Speaker": "Speaker 1", "text": "Vidím, teď teď vám to takhle zvětším. Přepnu jsem ho do plánovacího módu."}, {"StartTime": 5437000, "EndTime": 5437000, "VoiceStart": 5437000, "VoiceEnd": 5437000, "Speaker": "Speaker 1", "text": "A dám mu enter."}, {"StartTime": 5440000, "EndTime": 5440000, "VoiceStart": 5440000, "VoiceEnd": 5440000, "Speaker": "Speaker 1", "text": "A v plánovacím módu on jenom naplánuje, mi jenom řekne, jaké grafy můžeme vygenerovat."}, {"StartTime": 5447000, "EndTime": 5447000, "VoiceStart": 5447000, "VoiceEnd": 5447000, "Speaker": "Speaker 1", "text": "Pak to přepnu do act, jako aby to dělal a on mi vygeneruje jakýkoliv typy grafů, cokoliv."}, {"StartTime": 5454000, "EndTime": 5454000, "VoiceStart": 5454000, "VoiceEnd": 5454000, "Speaker": "Speaker 1", "text": "Bodový grafů, vygeneruj, vygeneruj všechny typy grafů."}, {"StartTime": 5459000, "EndTime": 5459000, "VoiceStart": 5459000, "VoiceEnd": 5459000, "Speaker": "Speaker 1", "text": "Vygeneruj do PNG všechny typy grafů."}, {"StartTime": 5465000, "EndTime": 5465000, "VoiceStart": 5465000, "VoiceEnd": 5465000, "Speaker": "Speaker 1", "text": "Act."}, {"StartTime": 5467000, "EndTime": 5467000, "VoiceStart": 5467000, "VoiceEnd": 5467000, "Speaker": "Speaker 1", "text": "A pracuje."}, {"StartTime": 5469000, "EndTime": 5469000, "VoiceStart": 5469000, "VoiceEnd": 5469000, "Speaker": "Speaker 1", "text": "Jo, potom tady je vidět i celková cena, kdybych to měl placený."}, {"StartTime": 5473000, "EndTime": 5473000, "VoiceStart": 5473000, "VoiceEnd": 5473000, "Speaker": "Speaker 1", "text": "Jo, vy to budete mít zadarmo, ale stálo by mě to 5 Kč teď už."}, {"StartTime": 5477000, "EndTime": 5477000, "VoiceStart": 5477000, "VoiceEnd": 5477000, "Speaker": "Speaker 1", "text": "Tam je právě ten rozdíl v rychlejch modelech, chytrejch modelech a tak."}, {"StartTime": 5482000, "EndTime": 5482000, "VoiceStart": 5482000, "VoiceEnd": 5482000, "Speaker": "Speaker 1", "text": "Jo, takže eh teď já nevím, jestli už to udělal."}, {"StartTime": 5487000, "EndTime": 5487000, "VoiceStart": 5487000, "VoiceEnd": 5487000, "Speaker": "Speaker 1", "text": "Jo, on to teprv píše ty kódy."}, {"StartTime": 5490000, "EndTime": 5490000, "VoiceStart": 5490000, "VoiceEnd": 5490000, "Speaker": "Speaker 1", "text": "Teď píše normálně na to kódy a eh stejně tak jako ten chat GPT."}, {"StartTime": 5494000, "EndTime": 5494000, "VoiceStart": 5494000, "VoiceEnd": 5494000, "Speaker": "Speaker 1", "text": "Když si potom na to zvyknete, tak mezitím není v podstatě žádný rozdíl."}, {"StartTime": 5498000, "EndTime": 5498000, "VoiceStart": 5498000, "VoiceEnd": 5498000, "Speaker": "Speaker 1", "text": "Teď teď to, teď to vygeneroval."}, {"StartTime": 5500000, "EndTime": 5500000, "VoiceStart": 5500000, "VoiceEnd": 5500000, "Speaker": "Speaker 1", "text": "A teď se můžeme podívat na koláč graf."}, {"StartTime": 5503000, "EndTime": 5503000, "VoiceStart": 5503000, "VoiceEnd": 5503000, "Speaker": "Speaker 1", "text": "A teď tady je struktura populace v Praze třeba."}, {"StartTime": 5506000, "EndTime": 5506000, "VoiceStart": 5506000, "VoiceEnd": 5506000, "Speaker": "Speaker 1", "text": "A já mu řeknu, jo, to tohle je jeden a tady je graf populace PNG."}, {"StartTime": 5512000, "EndTime": 5512000, "VoiceStart": 5512000, "VoiceEnd": 5512000, "Speaker": "Speaker 1", "text": "A a to je právě supr, je tam chyba."}, {"StartTime": 5515000, "EndTime": 5515000, "VoiceStart": 5515000, "VoiceEnd": 5515000, "Speaker": "Speaker 1", "text": "A já mu to normálně takhle dám jako print screen a řeknu: "Máš to špatně.""}, {"StartTime": 5520000, "EndTime": 5520000, "VoiceStart": 5520000, "VoiceEnd": 5520000, "Speaker": "Speaker 1", "text": "máš to špatně. Vidíš na obrázku, že je to špatně a on to opraví."}, {"StartTime": 5534000, "EndTime": 5534000, "VoiceStart": 5534000, "VoiceEnd": 5534000, "Speaker": "Speaker 1", "text": "Jo, prostě nebát se, že oni normálně dělají chyby. Takže eh, je to prostě jak jak nějaký v současnosti junior až medior, jo. Na nějaké věci prostě až PhD."}, {"StartTime": 5550000, "EndTime": 5550000, "VoiceStart": 5550000, "VoiceEnd": 5550000, "Speaker": "Speaker 2", "text": "Eh, já mám dotaz."}, {"StartTime": 5552000, "EndTime": 5552000, "VoiceStart": 5552000, "VoiceEnd": 5552000, "Speaker": "Speaker 1", "text": "To jděte."}, {"StartTime": 5554000, "EndTime": 5554000, "VoiceStart": 5554000, "VoiceEnd": 5554000, "Speaker": "Speaker 2", "text": "Eh, je to trošku teď mimo, ale chtěla jsem se zeptat, jestli doporučujete nějakej jinej program na vygen vygenerování obrazy, který nebude jenom chat GPT."}, {"StartTime": 5566000, "EndTime": 5566000, "VoiceStart": 5566000, "VoiceEnd": 5566000, "Speaker": "Speaker 1", "text": "Vygenerování čeho teď?"}, {"StartTime": 5568000, "EndTime": 5568000, "VoiceStart": 5568000, "VoiceEnd": 5568000, "Speaker": "Speaker 2", "text": "Obrazy."}, {"StartTime": 5569000, "EndTime": 5569000, "VoiceStart": 5569000, "VoiceEnd": 5569000, "Speaker": "Speaker 1", "text": "Obrazu. Obrazu. Obrazů. Obrazů. Jak jsem říkal, eh, já používám a teď záleží typově jakej, jako co jakej je typ. Existuje Mid Journey, to nevím, jestli znáte."}, {"StartTime": 5583000, "EndTime": 5583000, "VoiceStart": 5583000, "VoiceEnd": 5583000, "Speaker": "Speaker 2", "text": "Jestli to doporučujete?"}, {"StartTime": 5584000, "EndTime": 5584000, "VoiceStart": 5584000, "VoiceEnd": 5584000, "Speaker": "Speaker 1", "text": "Prosím?"}, {"StartTime": 5585000, "EndTime": 5585000, "VoiceStart": 5585000, "VoiceEnd": 5585000, "Speaker": "Speaker 2", "text": "Jestli to doporučujete ten Mid Journey?"}, {"StartTime": 5588000, "EndTime": 5588000, "VoiceStart": 5588000, "VoiceEnd": 5588000, "Speaker": "Speaker 1", "text": "Eh, já osobně ale to je už musel by vám nějaký kolega nebo vy byste si museli vzít tohle a nechat to napsat kódy na tady ten replicate. Zase je to úplně není to žádná raketová věda, my my klidně v komunitě s tím pomáháme na Discordu a tady je těch obrazovejch hodně a dá se do toho i klikat v podstatě. Jo. A tady, eh, ten tady je hodně různých modelů, který každý to dělá trošku jinak. Tady ten Black Forest jsou Němci a tady tenhleten model, ten flax context max je jedinej nebo jeden z mála modelů, který eh umí modifikovat obrázky přímo v obrázcích. To je jako jeho výhoda. Jinak je to fakt hodně o vkusu. Eh, chat GPT, říkám, my s tím tady ty zprávy AI, jak jsem to."}, {"StartTime": 5640000, "EndTime": 5640000, "VoiceStart": 5640000, "VoiceEnd": 5640000, "Speaker": "Speaker 1", "text": "opravdu záleží, jestli třeba do těch obrázků chcete mít text, protože vidíte, že chat GPT umí, umí dělat správně text. To je pro ně velký problém, jo? Obzvlášť čeština. Když chcete mít text v obrázku správně česky, tak pro většinu modelů, většinu AI, kromě toho chat GPT je to problém to tam napsat česky s háčkama, s čárkama a správně. Oni tam často psali, já když bych se podíval na nějaký starý posty, co tady jsou, teď bych musel jít asi hodně hodně dozadu, tak ono to podle toho často můžete AI AI obrázky poznat, že ten text je prostě nesmyslnej, že jsou tam znaky teď hledám, jestli to tady najdu. Je to prostě vidět, je tam gibrish, je to nesmyslnej text. Oni dřív neuměli text. Nevím, jestli tady to nebude náhodou. Vidíte, že to ň je jinak. Jo, že i i Pizeň udělal místo Plzeň udělal Pizeň, jo? Takže tam hodně záleží, jestli je text v obrázku nebo ne. Pokud ne, tak tohle je fakt všechno z chat GPT a to u obrázku je to o vkusu jako je to, my journey, a my jsme ho přestali používat. Nemá to žádné jako úplně přidanou hodnotu obzvlášť, když máte placený chat GPT, no. Takže je to o zkoušení, a on je i tady i to Google AI Studio. Tady si můžete taky úplně zadarmo generovat. Tady je generate media, je to třetí záložka zvrchu a tady máte Imagine a tady je třeba Tak chci žlutýho papouška v lese a teď Imagine 30 ho vygenerujete. A tady to máte."}, {"StartTime": 5760000, "EndTime": 5760000, "VoiceStart": 5760000, "VoiceEnd": 5760000, "Speaker": "Speaker 1", "text": "zadarmo, akorát zase jsou tam limity, jo."}, {"StartTime": 5764000, "EndTime": 5764000, "VoiceStart": 5764000, "VoiceEnd": 5764000, "Speaker": "Speaker 1", "text": "Je to hodně hodně individuální, jo."}, {"StartTime": 5769000, "EndTime": 5769000, "VoiceStart": 5769000, "VoiceEnd": 5769000, "Speaker": "Speaker 1", "text": "To se těžko říká."}, {"StartTime": 5774000, "EndTime": 5774000, "VoiceStart": 5774000, "VoiceEnd": 5774000, "Speaker": "Speaker 1", "text": "Ale já, jestli jestli vám chat GPT stačí, tak jako není moc důvod někam přecházet. Tady to si říkám, můžete vyzkoušet zadarmo, ten to Google AI Studio."}, {"StartTime": 5782000, "EndTime": 5782000, "VoiceStart": 5782000, "VoiceEnd": 5782000, "Speaker": "Speaker 1", "text": "A kde je tady reset? Jo."}, {"StartTime": 5785000, "EndTime": 5785000, "VoiceStart": 5785000, "VoiceEnd": 5785000, "Speaker": "Speaker 1", "text": "Clear chat delete."}, {"StartTime": 5786000, "EndTime": 5786000, "VoiceStart": 5786000, "VoiceEnd": 5786000, "Speaker": "Speaker 1", "text": "A tady můžu napsat eh"}, {"StartTime": 5794000, "EndTime": 5794000, "VoiceStart": 5794000, "VoiceEnd": 5794000, "Speaker": "Speaker 1", "text": "Ale oni vám často budou odmítat. Když řeknu, že to má být ve stylu Marvelu, tak mi to pravděpodobně odmítne."}, {"StartTime": 5800000, "EndTime": 5800000, "VoiceStart": 5800000, "VoiceEnd": 5800000, "Speaker": "Speaker 1", "text": "Jo, ty oni mají problém s copyrightama a eh"}, {"StartTime": 5806000, "EndTime": 5806000, "VoiceStart": 5806000, "VoiceEnd": 5806000, "Speaker": "Speaker 1", "text": "když vám AI něco, ale, no tak tak neodmítla, jo."}, {"StartTime": 5811000, "EndTime": 5811000, "VoiceStart": 5811000, "VoiceEnd": 5811000, "Speaker": "Speaker 1", "text": "Nebát se toho, často odmítaj dělat smlouvy, často odmítají, jakmile to jde trochu do farma, tak to odmítají."}, {"StartTime": 5819000, "EndTime": 5819000, "VoiceStart": 5819000, "VoiceEnd": 5819000, "Speaker": "Speaker 1", "text": "Ale dají se uťat takzvaně."}, {"StartTime": 5821000, "EndTime": 5821000, "VoiceStart": 5821000, "VoiceEnd": 5821000, "Speaker": "Speaker 1", "text": "Když ho chvíli přesvědčujete, že na to máte práva, že je to od vás, normálně nebát se, když něco zamítne a zkusit ho přesvědčit a on se normálně v 50 až 80 % případů normálně nechá přesvědčit."}, {"StartTime": 5833000, "EndTime": 5833000, "VoiceStart": 5833000, "VoiceEnd": 5833000, "Speaker": "Speaker 1", "text": "Jo. U obrázků, když to zamítne, tak to přesvědčit nejde."}, {"StartTime": 5836000, "EndTime": 5836000, "VoiceStart": 5836000, "VoiceEnd": 5836000, "Speaker": "Speaker 1", "text": "Tak je potřeba ten promt, to zadání přepsat."}, {"StartTime": 5840000, "EndTime": 5840000, "VoiceStart": 5840000, "VoiceEnd": 5840000, "Speaker": "Speaker 1", "text": "A zase na na zadání obrázků vy můžete, že chcete kočku, takhle a můžete nechat AI vygenerovat to zadání."}, {"StartTime": 5848000, "EndTime": 5848000, "VoiceStart": 5848000, "VoiceEnd": 5848000, "Speaker": "Speaker 1", "text": "A ono bude mnohem přesnější, lepší, detailnější a eh jako rozvine tu vaši myšlenku a a"}, {"StartTime": 5857000, "EndTime": 5857000, "VoiceStart": 5857000, "VoiceEnd": 5857000, "Speaker": "Speaker 1", "text": "pro toho, pro tu druhou stranu, pro tu další AI je lepší ten hutnější promt, no."}, {"StartTime": 5862000, "EndTime": 5862000, "VoiceStart": 5862000, "VoiceEnd": 5862000, "Speaker": "Speaker 1", "text": "Jestli to má být fotorealistické nebo jestli to má být komixové, jestli to má být černobílé."}, {"StartTime": 5867000, "EndTime": 5867000, "VoiceStart": 5867000, "VoiceEnd": 5867000, "Speaker": "Speaker 1", "text": "Jo, já to můžu vzít, vyresetovat a říct, eh, že to chci černobílý, jo."}, {"StartTime": 5877000, "EndTime": 5877000, "VoiceStart": 5877000, "VoiceEnd": 5877000, "Speaker": "Speaker 1", "text": "Ale obrázky jsou prostě loterie, no."}, {"StartTime": 5885000, "EndTime": 5885000, "VoiceStart": 5885000, "VoiceEnd": 5885000, "Speaker": "Speaker 1", "text": "Jo, teď jsem mu řekl, že to chci černobílý a tužkou a mám z toho todle."}, {"StartTime": 5891000, "EndTime": 5891000, "VoiceStart": 5891000, "VoiceEnd": 5891000, "Speaker": "Speaker 1", "text": "Je to hodně my říkám, my jsme my už používáme jenom tady na obrázky chat chat GPT."}, {"StartTime": 5901000, "EndTime": 5901000, "VoiceStart": 5901000, "VoiceEnd": 5901000, "Speaker": "Speaker 1", "text": "Ale Google to má, ten Imagine je taky dobrej podle mě. Jo ty ty velký firmy to vyhrajou, protože mají nejvíc dat, takže žádný asi velmi pravděpodobně žádný jiný firmy nemají smysl než Open AI, čili chat GPT nebo Google, tady ten Imagine."}, {"StartTime": 5922000, "EndTime": 5922000, "VoiceStart": 5922000, "VoiceEnd": 5922000, "Speaker": "Speaker 2", "text": "Máte k tomu dnešnímu ještě něco? Popřípadě k tomu můžete napsat nějaký připomínky, my vám k tomu kolegyně vám k tomu pošle zpracovaný materiály. A ve čtvrtek máme mít další, ne, jestli se nepletu?"}, {"StartTime": 5935000, "EndTime": 5935000, "VoiceStart": 5935000, "VoiceEnd": 5935000, "Speaker": "Speaker 1", "text": "Ano, ano."}, {"StartTime": 5937000, "EndTime": 5937000, "VoiceStart": 5937000, "VoiceEnd": 5937000, "Speaker": "Speaker 2", "text": "čas Můžete můžete prostě"}, {"StartTime": 5939000, "EndTime": 5939000, "VoiceStart": 5939000, "VoiceEnd": 5939000, "Speaker": "Speaker 1", "text": "můžete prostě na to jakkoliv reagovat za vás, mít připomínky a dotazy klidně a tak."}, {"StartTime": 5947000, "EndTime": 5947000, "VoiceStart": 5947000, "VoiceEnd": 5947000, "Speaker": "Speaker 2", "text": "Vy jste potom hovořil ještě o těch AI agentech. to budeme probírat i příštích příštích? Jo."}, {"StartTime": 5953000, "EndTime": 5953000, "VoiceStart": 5953000, "VoiceEnd": 5953000, "Speaker": "Speaker 1", "text": "Jo jo, ale ale ta podstata to toho už jste viděli v akci. AI agent je to, čemu jsem řekl naprogramuj to. Jo? to je AI agent, protože já agentovi můžu říct sežeň si data z internetu a udělej mi prezentaci o Petru Pavlovi a agenský způsob znamená, že on si třeba udělá sám pro sebe plán, rozvrhne si to a plní úkol po úkolu a dělá to vlastně jakoby za mě. Má nějaký cíl, jeho cíl je udělat prezentaci a má pro to nějaký nástroje. Jo? To se fakt jako permomník s kladívkama asi si to jde představit, prostě jako trpaslíka, jeden má kladívko, jeden má pilku a oni se umějí i bavit spolu a jeden udělá tu část, že přinese něco z internetu a"}]}

